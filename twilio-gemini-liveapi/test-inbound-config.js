#!/usr/bin/env node

const { ScriptManager } = require('./src/scripts/script-manager.js');

async function testInboundConfig() {
    console.log('🧪 Testing inbound call configuration...');
    
    try {
        const scriptManager = new ScriptManager();
        
        // Test current incoming script
        console.log('\n1. Testing current incoming script...');
        const currentScript = scriptManager.getCurrentIncomingScript();
        console.log('Current incoming script:', currentScript);
        
        if (currentScript) {
            const config = scriptManager.getScriptConfig(currentScript.id, true);
            console.log('Script config:', {
                hasInstructions: !!config?.aiInstructions,
                instructionsLength: config?.aiInstructions?.length || 0,
                voice: config?.voice,
                model: config?.model,
                scriptId: config?.scriptId
            });
        }
        
        // Test default script loading (ID 7 = incoming campaign 1)
        console.log('\n2. Testing default script (ID 7)...');
        const defaultScript = scriptManager.getScriptConfig(7, true);
        console.log('Default script config:', {
            hasInstructions: !!defaultScript?.aiInstructions,
            instructionsLength: defaultScript?.aiInstructions?.length || 0,
            voice: defaultScript?.voice,
            model: defaultScript?.model,
            scriptId: defaultScript?.scriptId
        });
        
        // Test all incoming scripts
        console.log('\n3. Testing all incoming scripts...');
        const allIncomingScripts = scriptManager.getIncomingScripts();
        console.log('Available incoming scripts:', allIncomingScripts.length);
        allIncomingScripts.forEach(script => {
            console.log(`  - ID: ${script.id}, Name: ${script.name}`);
        });
        
        console.log('\n✅ Inbound configuration test completed');
        
    } catch (error) {
        console.error('❌ Error testing inbound config:', error);
    }
}

testInboundConfig();
