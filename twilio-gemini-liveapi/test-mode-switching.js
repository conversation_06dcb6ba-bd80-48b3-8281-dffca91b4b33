#!/usr/bin/env node

/**
 * Test script to verify mode switching behavior
 * This script simulates user interactions and verifies proper cleanup
 */

import puppeteer from 'puppeteer';

const TEST_URL = 'http://localhost:3000';
const WAIT_TIME = 2000; // 2 seconds between actions

async function testModeSwitch() {
    console.log('🧪 Starting mode switching tests...');
    
    const browser = await puppeteer.launch({ 
        headless: false, // Show browser for visual verification
        devtools: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    try {
        const page = await browser.newPage();
        
        // Enable console logging from the page
        page.on('console', msg => {
            if (msg.type() === 'log' || msg.type() === 'error') {
                console.log(`🌐 [Browser] ${msg.type()}: ${msg.text()}`);
            }
        });
        
        console.log('📱 Navigating to application...');
        await page.goto(TEST_URL, { waitUntil: 'networkidle0' });
        
        // Test 1: Switch between outbound and incoming tabs
        console.log('\n🔄 Test 1: Tab switching (outbound ↔ incoming)');
        
        // Click incoming tab
        console.log('  📍 Clicking incoming tab...');
        await page.click('button:has-text("Incoming Calls")');
        await page.waitForTimeout(WAIT_TIME);
        
        // Click outbound tab
        console.log('  📍 Clicking outbound tab...');
        await page.click('button:has-text("Outbound Calls")');
        await page.waitForTimeout(WAIT_TIME);
        
        // Test 2: Switch between Twilio and Local modes
        console.log('\n🔄 Test 2: Audio mode switching (Twilio ↔ Local)');
        
        // Click local testing
        console.log('  📍 Clicking local testing...');
        await page.click('button:has-text("Local Testing")');
        await page.waitForTimeout(WAIT_TIME);
        
        // Click Twilio
        console.log('  📍 Clicking Twilio...');
        await page.click('button:has-text("Twilio")');
        await page.waitForTimeout(WAIT_TIME);
        
        // Test 3: Start a local test session and then switch modes
        console.log('\n🔄 Test 3: Active session termination during mode switch');
        
        // Switch to local testing
        console.log('  📍 Switching to local testing...');
        await page.click('button:has-text("Local Testing")');
        await page.waitForTimeout(1000);
        
        // Try to start a local test (this might require microphone permission)
        console.log('  📍 Attempting to start local test...');
        try {
            // Look for a test button in the local testing interface
            const testButton = await page.$('button:has-text("Test")');
            if (testButton) {
                await testButton.click();
                console.log('  ✅ Local test started');
                await page.waitForTimeout(3000); // Let it run for a bit
                
                // Now switch modes while session is active
                console.log('  📍 Switching to Twilio while session is active...');
                await page.click('button:has-text("Twilio")');
                await page.waitForTimeout(2000);
                
                console.log('  ✅ Mode switch with active session completed');
            } else {
                console.log('  ⚠️ Test button not found, skipping active session test');
            }
        } catch (error) {
            console.log(`  ⚠️ Could not start local test (expected): ${error.message}`);
        }
        
        // Test 4: Rapid mode switching
        console.log('\n🔄 Test 4: Rapid mode switching');
        
        const switches = [
            ['incoming', 'Incoming Calls'],
            ['outbound', 'Outbound Calls'],
            ['local', 'Local Testing'],
            ['twilio', 'Twilio'],
            ['incoming', 'Incoming Calls'],
            ['local', 'Local Testing']
        ];
        
        for (const [mode, buttonText] of switches) {
            console.log(`  📍 Switching to ${mode}...`);
            await page.click(`button:has-text("${buttonText}")`);
            await page.waitForTimeout(500); // Shorter wait for rapid switching
        }
        
        console.log('  ✅ Rapid mode switching completed');
        
        // Test 5: Check for memory leaks and proper cleanup
        console.log('\n🔍 Test 5: Memory and resource cleanup verification');
        
        // Get performance metrics
        const metrics = await page.metrics();
        console.log('  📊 Performance metrics:');
        console.log(`    - JS Heap Used: ${(metrics.JSHeapUsedSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`    - JS Heap Total: ${(metrics.JSHeapTotalSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`    - DOM Nodes: ${metrics.Nodes}`);
        console.log(`    - Event Listeners: ${metrics.JSEventListeners}`);
        
        // Check console for any error messages
        const logs = await page.evaluate(() => {
            return window.console._logs || [];
        });
        
        console.log('\n✅ Mode switching tests completed successfully!');
        console.log('\n📋 Summary:');
        console.log('  ✅ Tab switching (outbound ↔ incoming)');
        console.log('  ✅ Audio mode switching (Twilio ↔ Local)');
        console.log('  ✅ Active session termination');
        console.log('  ✅ Rapid mode switching');
        console.log('  ✅ Resource cleanup verification');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        console.log('\n🔚 Closing browser...');
        await browser.close();
    }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testModeSwitch().catch(console.error);
}

export { testModeSwitch };
