# Mode Switching Test Checklist

## Overview
This checklist verifies that mode switching properly cleans up resources and resets state without conflicts or resource leaks.

## Test Environment
- Frontend: http://localhost:3000
- Backend: http://localhost:3101
- Browser: Chrome/Firefox with DevTools open

## Pre-Test Setup
1. ✅ Start backend server (`npm run dev` in root)
2. ✅ Start frontend server (`npm run dev` in call-center-frontend)
3. ✅ Open browser to http://localhost:3000
4. ✅ Open DevTools Console tab to monitor logs

## Test Cases

### Test 1: Basic Tab Switching (No Active Sessions)
**Objective**: Verify clean switching between outbound and incoming tabs

1. **Initial State**: Outbound tab active, no sessions running
2. **Action**: Click "Incoming Calls" tab
3. **Expected Results**:
   - ✅ Tab switches to incoming
   - ✅ Toast notification: "Switched to incoming calls"
   - ✅ UI updates to show incoming interface
   - ✅ No error messages in console

4. **Action**: Click "Outbound Calls" tab
5. **Expected Results**:
   - ✅ Tab switches to outbound
   - ✅ Toast notification: "Switched to outbound calls"
   - ✅ UI updates to show outbound interface
   - ✅ No error messages in console

### Test 2: Basic Audio Mode Switching (No Active Sessions)
**Objective**: Verify clean switching between Twilio and Local modes

1. **Initial State**: Twilio mode active, no sessions running
2. **Action**: Click "Local Testing" button
3. **Expected Results**:
   - ✅ Mode switches to local testing
   - ✅ Toast notification: "Switched to local mode"
   - ✅ UI updates to show local testing interface
   - ✅ No error messages in console

4. **Action**: Click "Twilio" button
5. **Expected Results**:
   - ✅ Mode switches to Twilio
   - ✅ Toast notification: "Switched to Twilio mode"
   - ✅ UI updates to show Twilio interface
   - ✅ No error messages in console

### Test 3: Tab Switching with Active Local Session
**Objective**: Verify proper session termination when switching tabs during active local testing

1. **Setup**: Switch to "Local Testing" mode
2. **Action**: Click "Test" button to start local audio session
3. **Verify**: Session starts (microphone access, "Connected" status)
4. **Action**: Click "Incoming Calls" tab while session is active
5. **Expected Results**:
   - ✅ Loading toast: "Switching from outbound to incoming..."
   - ✅ Yellow termination indicator appears
   - ✅ Audio session terminates (microphone stops, WebSocket closes)
   - ✅ Success toast: "Successfully switched to incoming calls"
   - ✅ Company status resets from "in-progress" to normal
   - ✅ Audio status resets to "Ready to start local testing"
   - ✅ Console shows cleanup logs

### Test 4: Audio Mode Switching with Active Session
**Objective**: Verify proper session termination when switching audio modes during active session

1. **Setup**: Local Testing mode with active session
2. **Action**: Click "Twilio" button while session is active
3. **Expected Results**:
   - ✅ Loading toast: "Switching from local testing to Twilio..."
   - ✅ Yellow termination indicator appears
   - ✅ All audio resources cleaned up
   - ✅ WebSocket connections closed
   - ✅ Success toast: "Successfully switched to Twilio mode"
   - ✅ State completely reset

### Test 5: Rapid Mode Switching
**Objective**: Verify system handles rapid mode changes without conflicts

1. **Action**: Rapidly click between modes (5-6 switches in quick succession)
   - Outbound → Incoming → Local → Twilio → Incoming → Local
2. **Expected Results**:
   - ✅ No JavaScript errors in console
   - ✅ UI remains responsive
   - ✅ Final state is consistent with last selection
   - ✅ No stuck loading states
   - ✅ No memory leaks (check DevTools Memory tab)

### Test 6: Button State During Termination
**Objective**: Verify buttons are properly disabled during session termination

1. **Setup**: Start local session
2. **Action**: Click mode switch button
3. **Expected Results**:
   - ✅ Buttons show loading spinners during termination
   - ✅ Buttons are disabled (cursor: not-allowed)
   - ✅ Buttons re-enable after termination completes
   - ✅ Visual feedback is clear and consistent

### Test 7: Resource Cleanup Verification
**Objective**: Verify no resource leaks after multiple mode switches

1. **Setup**: Open DevTools → Performance/Memory tab
2. **Action**: Perform 10+ mode switches with some active sessions
3. **Verification**:
   - ✅ Memory usage doesn't continuously increase
   - ✅ No WebSocket connections left open (Network tab)
   - ✅ No audio contexts left in suspended state
   - ✅ Event listeners properly removed
   - ✅ No console errors about cleanup failures

### Test 8: Error Handling
**Objective**: Verify graceful handling of cleanup errors

1. **Setup**: Simulate network issues or force WebSocket errors
2. **Action**: Switch modes during problematic conditions
3. **Expected Results**:
   - ✅ Cleanup attempts continue despite errors
   - ✅ User gets appropriate feedback
   - ✅ Application doesn't crash or freeze
   - ✅ State eventually reaches consistent state

## Success Criteria
- ✅ All test cases pass
- ✅ No JavaScript errors in console
- ✅ No memory leaks detected
- ✅ Smooth user experience with clear feedback
- ✅ Proper resource cleanup in all scenarios
- ✅ Consistent application state after mode switches

## Notes
- Monitor browser console for cleanup logs (🧹, 🔄, ✅ prefixes)
- Check Network tab for WebSocket connection management
- Use Memory tab to verify no resource leaks
- Test with both Chrome and Firefox for compatibility
