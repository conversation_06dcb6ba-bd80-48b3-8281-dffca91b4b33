import { endSession } from './session-utils.js';
import { websocketLogger } from '../utils/logger.js';
import { globalHeartbeatManager } from './heartbeat-manager.js';
import pkg from '@google/genai';
const { Modality } = pkg;

// Common local testing flow handler (for both inbound and outbound testing)
export function handleLocalTestingFlow(connection, deps) {
    const { config } = deps;
    const enableDetailedLogging = config?.environment?.enableDetailedLogging;
    
    if (enableDetailedLogging) {
        console.log(`🔥🔥🔥 HANDLELOCALTESTINGFLOW CALLED! FlowType: ${deps.flowType} 🔥🔥🔥`);
    }
    
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        summaryManager,
        lifecycleManager,
        recoveryManager,
        transcriptionManager,
        flowType,
        getSessionConfig,
        isIncomingCall,
        SUMMARY_GENERATION_PROMPT
    } = deps;

    const sessionId = `${flowType}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    if (enableDetailedLogging) {
        console.log(`🔥 [${sessionId}] About to log testing session started`);
    }
    websocketLogger.info(`${flowType.toUpperCase()} testing session started`, { sessionId });
    if (enableDetailedLogging) {
        websocketLogger.debug(`Setting up message handler for ${flowType} testing`, { sessionId });
        websocketLogger.debug('Step 1: About to check WebSocket state', { sessionId });
    }

    try {
        if (enableDetailedLogging) {
            websocketLogger.debug('Connection object inspected', { sessionId, hasConnection: !!connection });
            websocketLogger.debug('Connection keys', { sessionId, keys: Object.keys(connection) });
            websocketLogger.debug('Socket object present', { sessionId, hasSocket: !!connection.socket });
        }

        // Try different possible WebSocket references
        const ws = connection.socket || connection;
        if (enableDetailedLogging) {
            websocketLogger.debug('Using WebSocket', { sessionId, hasWs: !!ws });
            websocketLogger.debug('WebSocket readyState', { sessionId, readyState: ws.readyState });
            websocketLogger.debug('WebSocket protocol', { sessionId, protocol: ws.protocol });
            websocketLogger.debug('Step 3: About to declare variables', { sessionId });
        }
    } catch (wsError) {
        console.error(`❌ [${sessionId}] Error checking WebSocket state:`, wsError);
        if (enableDetailedLogging) {
            console.error(`❌ [${sessionId}] Error stack:`, wsError.stack);
        }
        // Don't return early - continue with handler setup
        if (enableDetailedLogging) {
            websocketLogger.debug('Continuing with handler setup despite WebSocket error', { sessionId });
        }
    }

    let geminiSession = null;
    let isSessionActive = false;
    if (enableDetailedLogging) {
        websocketLogger.debug('Step 4: Variables declared, about to attach message handler', { sessionId });
    }

    // Use the correct WebSocket reference
    const ws = connection.socket || connection;
    if (enableDetailedLogging) {
        websocketLogger.debug('Attaching message handler', { sessionId, hasWs: !!ws });
    }

    // Store event listeners for cleanup
    const eventListeners = new Map();

    const messageHandler = async (message) => {
        if (enableDetailedLogging) {
            console.log(`🚨🚨🚨 [${sessionId}] MESSAGE HANDLER CALLED! 🚨🚨🚨`);
            websocketLogger.debug('MESSAGE HANDLER CALLED!', { sessionId });
            websocketLogger.debug('Raw message received', { sessionId, preview: message.toString().substring(0, 200) });
        }
        try {
            const data = JSON.parse(message);
            if (enableDetailedLogging) {
                console.log(`🔍 [${sessionId}] OUTBOUND TEST: Received message type: ${data.type}`);
                websocketLogger.debug(`Parsed message type: ${data.type}`, { sessionId });
                websocketLogger.debug('Message data keys', { sessionId, keys: Object.keys(data) });
                websocketLogger.debug('===== MESSAGE SWITCH DEBUG =====', { sessionId });
                websocketLogger.debug(`data.type = "${data.type}"`, { sessionId });
                websocketLogger.debug('About to enter switch statement', { sessionId });
                websocketLogger.debug('=====================================', { sessionId });
            }

            switch (data.type) {
            case 'start-session':
                if (enableDetailedLogging) {
                    console.log(`🔍 [${sessionId}] ENTERING START-SESSION HANDLER for ${flowType}`);
                }
                const sessionData = await handleLocalStartSession(sessionId, data, deps, connection, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager);
                geminiSession = sessionData.geminiSession;
                isSessionActive = sessionData.isSessionActive;
                if (enableDetailedLogging) {
                    console.log(`🔍 [${sessionId}] START-SESSION HANDLER COMPLETED, geminiSession: ${!!geminiSession}, isSessionActive: ${isSessionActive}`);
                }
                break;

            case 'audio-data':
                await handleAudioData(sessionId, data, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager, flowType);
                break;

            case 'text-message':
                await handleTextMessage(sessionId, data, geminiSession, isSessionActive, deps);
                break;

            case 'turn-complete':
                await handleTurnComplete(sessionId, geminiSession, isSessionActive, deps);
                break;

            case 'end-session':
                await handleEndSession(sessionId, deps, activeConnections, lifecycleManager);
                break;

            case 'request-summary':
                await handleRequestSummary(sessionId, deps, activeConnections, summaryManager, contextManager);
                break;

            case 'heartbeat':
                // Handle heartbeat messages silently - just acknowledge
                websocketLogger.debug(`Heartbeat received for ${flowType} testing`, { sessionId });
                break;

            case 'audio-response':
                // Handle audio-response events (frontend might be echoing back audio)
                websocketLogger.debug(`Audio response received for ${flowType} testing`, { sessionId });
                // Don't process these - they're just echoes from the frontend
                break;

            default:
                websocketLogger.warn(`Unknown ${flowType} testing event: ${data.type}`, { sessionId });
            }

        } catch (error) {
            console.error(`❌ [${sessionId}] Error processing ${flowType} testing message:`, error);
        }
    };

    // Store handlers for cleanup
    eventListeners.set('message', messageHandler);
    console.log(`🚨 [${sessionId}] ATTACHING MESSAGE HANDLER TO WebSocket`);
    console.log(`🚨 [${sessionId}] WebSocket readyState: ${ws.readyState}`);
    ws.on('message', messageHandler);

    const closeHandler = async (code, reason) => {
        websocketLogger.info(`${flowType.toUpperCase()} testing connection closed`, {
            sessionId,
            code,
            reason: reason || 'No reason'
        });

        // Stop heartbeat monitoring
        globalHeartbeatManager.stopHeartbeat(sessionId);
        // This is a user-initiated session end via connection close
        const connectionData = activeConnections.get(sessionId);
        if (connectionData) {
            // Clean up Deepgram transcription
            if (connectionData.deepgramConnection) {
                deps.transcriptionManager.closeTranscription(sessionId);
            }

            if (deps.lifecycleManager) {
                await deps.lifecycleManager.requestSessionEnd(sessionId, connectionData, 'user_close_testing');
            } else {
                endSession(sessionId, deps, 'connection_closed');
            }
        } else {
            endSession(sessionId, deps, 'connection_closed');
        }

        // Clean up event listeners
        for (const [event, handler] of eventListeners) {
            ws.removeListener(event, handler);
        }
        eventListeners.clear();
    };

    const errorHandler = async (error) => {
        websocketLogger.error(`${flowType.toUpperCase()} testing error`, error, { sessionId });
        websocketLogger.error('Error stack', { sessionId, stack: error.stack });
        // WebSocket error in testing - try to recover session instead of ending it
        const connectionData = activeConnections.get(sessionId);
        if (connectionData && recoveryManager && contextManager.canRecover(sessionId)) {
            websocketLogger.info('Testing WebSocket error detected, attempting session recovery', { sessionId });
            contextManager.markSessionInterrupted(sessionId, 'testing_websocket_error');
            // Don't end session immediately - let recovery manager handle it
            setTimeout(async () => {
                await recoveryManager.recoverSession(sessionId, 'testing_websocket_error', activeConnections);
            }, 1000);
        } else {
            // Clean up Deepgram transcription before ending session
            if (connectionData && connectionData.deepgramConnection) {
                deps.transcriptionManager.closeTranscription(sessionId);
            }
            // Only end session if recovery is not possible
            endSession(sessionId, { ...deps, transcriptionManager }, 'connection_error');
        }
    };

    // Register all event listeners and store for cleanup
    eventListeners.set('close', closeHandler);
    eventListeners.set('error', errorHandler);
    ws.on('close', closeHandler);
    ws.on('error', errorHandler);
}

// Helper functions for message handling
async function handleLocalStartSession(sessionId, data, deps, connection, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager) {
    console.log(`🔍 [${sessionId}] ===== ENTERED START-SESSION CASE =====`);
    console.log(`🚀 [${sessionId}] Starting ${flowType} testing session`);

    try {
        // Get flow-specific configuration
        console.log(`🔍 DEBUG: [${sessionId}] About to call getSessionConfig for ${flowType}`);
        let sessionConfig = getSessionConfig();
        console.log(`🔍 DEBUG: [${sessionId}] getSessionConfig returned:`, !!sessionConfig);

        if (sessionConfig) {
            console.log(`🔍 DEBUG: [${sessionId}] Initial session config:`, {
                hasAiInstructions: !!sessionConfig.aiInstructions,
                aiInstructionsLength: sessionConfig.aiInstructions?.length || 0,
                voice: sessionConfig.voice,
                model: sessionConfig.model,
                scriptType: sessionConfig.scriptType,
                scriptId: sessionConfig.scriptId
            });
        } else {
            console.error(`❌ [${sessionId}] getSessionConfig returned null/undefined for ${flowType}`);
        }

        // Allow override from client for testing - use campaign script as-is
        if (data.aiInstructions) {
            sessionConfig.aiInstructions = data.aiInstructions;
        }
        if (data.voice) {
            sessionConfig.voice = deps.voiceManager.getValidGeminiVoice(data.voice);
        }
        if (data.model) {
            sessionConfig.model = deps.modelManager.getValidGeminiModel(data.model);
        }
        if (data.scriptId) {
            // Load specific script for testing
            try {
                const testConfig = deps.scriptManager.getScriptConfig(data.scriptId, isIncomingCall);
                if (testConfig) {
                    sessionConfig = {
                        ...testConfig,
                        aiInstructions: `[TESTING MODE] ${testConfig.aiInstructions}`,
                        isTestMode: true
                    };
                }
            } catch (error) {
                console.warn(`⚠️ [${sessionId}] Error loading test script ${data.scriptId}:`, error);
            }
        }

        // Store enhanced connection data
        const connectionData = {
            localWs: connection.socket || connection,
            sessionId,
            isSessionActive: false,
            summaryRequested: false,
            summaryReceived: false,
            summaryText: '',
            conversationLog: [],
            fullTranscript: [],
            speechTranscript: [],
            isIncomingCall,
            sessionType: 'local_test',
            flowType,
            sessionStartTime: Date.now(),
            lastActivity: Date.now(),
            targetName: sessionConfig.targetName || 'Test Contact',
            targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
            originalAIInstructions: sessionConfig.aiInstructions,
            scriptId: sessionConfig.scriptId,
            isTestMode: true,
            // CRITICAL: Add missing turn management properties
            lastAIResponse: Date.now(), // Track AI responsiveness
            responseTimeouts: 0, // Count consecutive timeouts
            connectionQuality: 'good', // Track connection quality
            lastContextSave: Date.now(), // For periodic context saving
            contextSaveInterval: null // For periodic context saving
        };
        activeConnections.set(sessionId, connectionData);

        // Track connection health
        healthMonitor.trackConnection(sessionId, 'connected', {
            flowType,
            isTestMode: true,
            scriptId: sessionConfig.scriptId
        });

        // Create Gemini session
        console.log(`🔍 DEBUG: [${sessionId}] About to create Gemini session...`);

        const sessionStartTime = Date.now();
        let geminiSession = null;
        let isSessionActive = false;

        try {
            // Use the correct model and voice from environment/managers
            const correctModel = sessionConfig.model || deps.GEMINI_DEFAULT_MODEL;
            const correctVoice = sessionConfig.voice || deps.GEMINI_DEFAULT_VOICE;
            console.log(`🔍 [${sessionId}] FINAL: Using model: "${correctModel}", voice: "${correctVoice}"`);

            console.log(`🔍 [${sessionId}] About to call geminiClient.live.connect...`);
            console.log(`🔍 [${sessionId}] geminiClient available: ${!!deps.sessionManager.geminiClient}`);
            console.log(`🔍 [${sessionId}] geminiClient.live available: ${!!deps.sessionManager.geminiClient?.live}`);
            
            const geminiStartTime = Date.now();
            console.log(`⏱️ [${sessionId}] Starting Gemini connection at ${new Date(geminiStartTime).toISOString()}`);
            
            // Define sendInitialInstructions function before connecting
            const sendInitialInstructions = async (session) => {
                // Use the session parameter - it should always be provided from onopen callback
                const activeSession = session;
                if (!sessionConfig.aiInstructions || !activeSession) {
                    console.warn(`⚠️ [${sessionId}] Missing AI instructions or Gemini session for initial setup`);
                    console.warn(`⚠️ [${sessionId}] aiInstructions: ${!!sessionConfig.aiInstructions}, activeSession: ${!!activeSession}`);
                    console.warn(`⚠️ [${sessionId}] session parameter: ${!!session}, geminiSession closure: ${!!geminiSession}`);
                    return;
                }

                try {
                    console.log(`🎯 [${sessionId}] Sending ${flowType} instructions (${sessionConfig.aiInstructions.length} chars)`);
                    console.log(`🎯 [${sessionId}] AI instructions preview: ${sessionConfig.aiInstructions.substring(0, 200)}...`);
                    console.log(`🔍 [${sessionId}] About to send initial instructions to activeSession: ${!!activeSession}`);

                    // For outbound calls, combine instructions with trigger in a single message
                    if (flowType === 'outbound_test') {
                        console.log(`🎤 [${sessionId}] Sending combined instructions + trigger for outbound call`);
                        const combinedMessage = `${sessionConfig.aiInstructions}\n\nThe call has been answered. Start speaking immediately according to your outbound campaign instructions.`;

                        await activeSession.sendClientContent({
                            turns: [{
                                role: 'user',
                                parts: [{
                                    text: combinedMessage
                                }]
                            }],
                            turnComplete: true
                        });
                        console.log(`✅ [${sessionId}] Combined outbound instructions + trigger sent successfully`);
                    } else {
                        // For inbound calls, send only the instructions
                        console.log(`📞 [${sessionId}] Sending instructions for inbound call - AI will wait for caller`);
                        await activeSession.sendClientContent({
                            turns: [{
                                role: 'user',
                                parts: [{
                                    text: sessionConfig.aiInstructions
                                }]
                            }],
                            turnComplete: true
                        });
                        console.log(`✅ [${sessionId}] Inbound instructions sent successfully`);
                    }

                    const instructionsSentTime = Date.now();
                    console.log(`✅ [${sessionId}] Initial AI instructions sent successfully`);
                    console.log(`⏱️ [${sessionId}] Total time from connection start to instructions sent: ${instructionsSentTime - geminiStartTime}ms`);
                    
                } catch (initError) {
                    console.error(`❌ [${sessionId}] Failed to send initial AI instructions:`, initError);
                    console.error(`❌ [${sessionId}] Error details:`, initError.stack);
                    console.error(`❌ [${sessionId}] This error might cause the Gemini session to close prematurely`);

                    // Check if the session is still active after the error
                    console.log(`🔍 [${sessionId}] Session state after error: activeSession=${!!activeSession}, isSessionActive=${isSessionActive}`);
                }
            };
            
            console.log(`🔍 [${sessionId}] About to create Gemini session with model: ${correctModel}`);
            console.log(`🔍 [${sessionId}] geminiClient available: ${!!deps.sessionManager.geminiClient}`);
            console.log(`🔍 [${sessionId}] geminiClient.live available: ${!!deps.sessionManager.geminiClient?.live}`);
            console.log(`🔍 [${sessionId}] geminiSession before connect: ${!!geminiSession}`);

            geminiSession = await deps.sessionManager.geminiClient.live.connect({
                model: correctModel,
                callbacks: {
                    onopen: async () => {
                        const openTime = Date.now();
                        const connectionDuration = openTime - geminiStartTime;
                        console.log(`✅ [${sessionId}] DIRECT Gemini session opened successfully`);
                        console.log(`⏱️ [${sessionId}] Gemini connection took ${connectionDuration}ms`);
                        console.log(`🔍 [${sessionId}] Session state after open: isSessionActive will be set to true`);

                        isSessionActive = true;
                        connectionData.isSessionActive = true;

                        // The geminiSession will be available after the connect() call completes
                        // We'll send instructions after the connection is fully established
                        console.log(`🚀 [${sessionId}] Session opened, will send instructions after connect() completes`);
                    },

                    onmessage: async (message) => {
                        try {
                            // Log every single Gemini message to debug why responses stop
                            console.log(`📨 [${sessionId}] DIRECT Gemini message received at ${new Date().toISOString()}`);
                            console.log(`📨 [${sessionId}] Message type/content:`, JSON.stringify(message, null, 2).substring(0, 500));
                            
                            // Log specific message types
                            if (message.setupComplete) {
                                console.log(`✅ [${sessionId}] Gemini setup complete - ready for audio`);
                            }
                            if (message.serverContent?.modelTurn?.parts?.[0]?.inlineData) {
                                if (!geminiSession.firstAudioResponseTime) {
                                    geminiSession.firstAudioResponseTime = Date.now();
                                    const responseDelay = geminiSession.firstAudioResponseTime - geminiStartTime;
                                    console.log(`🎵 [${sessionId}] Gemini sent FIRST AUDIO response!`);
                                    console.log(`⏱️ [${sessionId}] Time from connection to first audio: ${responseDelay}ms`);
                                } else {
                                    console.log(`🎵 [${sessionId}] Gemini sent AUDIO response!`);
                                }
                            }
                            if (message.serverContent?.modelTurn?.parts?.[0]?.text) {
                                console.log(`💬 [${sessionId}] Gemini sent TEXT response: ${message.serverContent?.modelTurn?.parts?.[0]?.text?.substring(0, 100)}`);
                            }
                            if (message.serverContent?.turnComplete) {
                                console.log(`🏁 [${sessionId}] Gemini turn complete`);
                            }
                            
                            // Handle setupComplete message - CRITICAL
                            if (message.setupComplete) {
                                console.log(`✅ [${sessionId}] DIRECT Gemini setup complete - session ready for conversation`);
                                return;
                            }

                            // Handle goAway message - CRITICAL
                            if (message.goAway) {
                                console.log(`⚠️ [${sessionId}] Gemini session ending - goAway received:`, message.goAway);
                                console.log(`🔄 [${sessionId}] Session will close in:`, message.goAway.timeLeft);
                                console.log(`🔄 [${sessionId}] IGNORING goAway - keeping session alive until user manually stops`);
                                return;
                            }

                            // Log Gemini API messages for debugging (excluding audio packets)
                            const hasAudio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
                            if (!hasAudio) {
                                console.log(`📨 [${sessionId}] DIRECT Gemini API message (non-audio):`, JSON.stringify(message, null, 2));
                            } else {
                                console.log(`🎵 [${sessionId}] DIRECT Gemini audio packet received (${message.serverContent?.modelTurn?.parts?.[0]?.inlineData?.data?.length || 0} bytes)`);

                                try {
                                    console.log(`🔍 [${sessionId}] AUDIO PACKET PROCESSING STARTED - about to process audio forwarding`);

                                    // Handle audio response from Gemini - SIMPLIFIED
                                    const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;

                                    console.log(`🔍 [${sessionId}] Audio forwarding debug: audio exists: ${!!audio}, data length: ${audio?.data?.length || 0}`);

                                    // Get connectionData from activeConnections since it's not in scope here
                                    const currentConnectionData = deps.activeConnections.get(sessionId);
                                    console.log(`🔍 [${sessionId}] ConnectionData debug: currentConnectionData exists: ${!!currentConnectionData}, localWs exists: ${!!currentConnectionData?.localWs}, localWs readyState: ${currentConnectionData?.localWs?.readyState}`);

                                    // ENHANCED CHECK - ensure WebSocket is still available and open
                                    if (audio && audio.data && audio.data.length > 0) {
                                        // Get fresh connection data to ensure we have the latest WebSocket state
                                        const freshConnectionData = deps.activeConnections.get(sessionId);
                                        const localWs = freshConnectionData?.localWs;
                                        
                                        console.log(`🔍 [${sessionId}] Audio forwarding check: audio=${!!audio}, data=${audio.data?.length || 0}, localWs=${!!localWs}, readyState=${localWs?.readyState}`);
                                        
                                        if (localWs && localWs.readyState === 1) { // WebSocket.OPEN
                                            console.log(`🔊 [${sessionId}] Sending audio response to client, size: ${audio.data?.length || 0}, mimeType: ${audio.mimeType}`);

                                            try {
                                                // Send audio back to client with metadata for proper playback
                                                localWs.send(JSON.stringify({
                                                    type: 'audio',
                                                    audio: audio.data,
                                                    mimeType: audio.mimeType // Include mime type for sample rate info
                                                }));
                                                console.log(`✅ [${sessionId}] Audio sent successfully to WebSocket`);
                                            } catch (sendError) {
                                                console.error(`❌ [${sessionId}] Error sending audio to WebSocket:`, sendError);
                                                console.error(`🔍 [${sessionId}] WebSocket state after error: ${localWs.readyState}`);
                                            }
                                        } else {
                                            console.warn(`⚠️ [${sessionId}] Cannot send audio: localWs=${!!localWs}, readyState=${localWs?.readyState}`);
                                            // If WebSocket is closed, mark session as inactive to prevent further processing
                                            if (localWs && localWs.readyState === 3) { // CLOSED
                                                console.log(`🔌 [${sessionId}] WebSocket closed, marking session inactive`);
                                                isSessionActive = false;
                                            }
                                        }
                                    } else {
                                        console.log(`📝 [${sessionId}] Received non-audio message from Gemini`);
                                    }
                                } catch (error) {
                                    console.error(`❌ [${sessionId}] Error in audio packet processing:`, error);
                                }
                            }

                            // Handle turnComplete message
                            if (message.serverContent?.turnComplete) {
                                console.log(`🎯 [${sessionId}] Turn complete - waiting for next user input, keeping session alive`);
                                return;
                            }

                            // Handle text response for summary collection and conversation logging
                            const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
                            if (text) {
                                console.log(`💬 [${sessionId}] DIRECT Gemini response: ${text.substring(0, 100)}...`);

                                // Update AI responsiveness tracking - use currentConnectionData for consistency
                                const currentConnectionData = deps.activeConnections.get(sessionId);
                                if (currentConnectionData) {
                                    currentConnectionData.lastAIResponse = Date.now();
                                    currentConnectionData.responseTimeouts = 0; // Reset timeout counter
                                    currentConnectionData.connectionQuality = 'good';
                                    
                                    // Add AI response to conversation log
                                    currentConnectionData.conversationLog.push({
                                        role: 'assistant',
                                        content: text,
                                        timestamp: Date.now()
                                    });
                                    console.log(`📝 [${sessionId}] Added AI response to conversation log (total entries: ${currentConnectionData.conversationLog.length})`);
                                }
                            }

                        } catch (error) {
                            console.error(`❌ [${sessionId}] Error processing DIRECT Gemini message:`, error);
                        }
                    },

                    onerror: (error) => {
                        console.error(`🚨 [${sessionId}] GEMINI API ERROR DETECTED 🚨`);
                        console.error(`❌ [${sessionId}] DIRECT Gemini session error:`, error);
                        console.error(`❌ [${sessionId}] Error type: ${typeof error}`);
                        console.error(`❌ [${sessionId}] Error message: ${error?.message || 'No message'}`);
                        console.error(`❌ [${sessionId}] Error details:`, JSON.stringify(error, Object.getOwnPropertyNames(error)));
                        
                        // Check for specific quota errors
                        const errorStr = JSON.stringify(error, Object.getOwnPropertyNames(error));
                        if (errorStr.includes('quota') || errorStr.includes('exceeded')) {
                            console.error(`🚨🚨🚨 [${sessionId}] QUOTA EXCEEDED ERROR 🚨🚨🚨`);
                            console.error(`💳 [${sessionId}] Check your Gemini API billing and quota limits!`);
                        }
                        
                        isSessionActive = false;
                    },

                    onclose: (event) => {
                        console.log(`🔌 [${sessionId}] DIRECT Gemini session closed`);
                        console.log(`🔍 [${sessionId}] Close event details:`, JSON.stringify(event, Object.getOwnPropertyNames(event)));
                        console.log(`🔍 [${sessionId}] Session was active for: ${Date.now() - sessionStartTime}ms`);
                        console.log(`🔍 [${sessionId}] Was session ever active? ${isSessionActive}`);
                        console.log(`🔍 [${sessionId}] Close code: ${event?.code}, reason: ${event?.reason}`);
                        console.log(`🔍 [${sessionId}] Close wasClean: ${event?.wasClean}`);

                        // Check if this is an unexpected close
                        if (isSessionActive && Date.now() - sessionStartTime < 5000) {
                            console.error(`❌ [${sessionId}] UNEXPECTED EARLY CLOSE! Session closed after only ${Date.now() - sessionStartTime}ms`);
                            console.error(`❌ [${sessionId}] This suggests an issue with initial instructions or session setup`);
                        }

                        isSessionActive = false;
                    }
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: correctVoice
                            }
                        }
                    }
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192
            });

            console.log(`🔍 [${sessionId}] geminiClient.live.connect() returned:`, !!geminiSession);
            console.log(`🔍 [${sessionId}] geminiSession type: ${typeof geminiSession}`);
            console.log(`🔍 [${sessionId}] geminiSession keys: ${Object.keys(geminiSession || {})}`);

            connectionData.geminiSession = geminiSession;
            console.log(`✅ [${sessionId}] DIRECT Gemini session created successfully for ${flowType} testing`);

            // Now that the session is fully created, send initial instructions
            console.log(`🚀 [${sessionId}] Session fully created, sending initial instructions now`);
            await sendInitialInstructions(geminiSession);

            // Start WebSocket heartbeat monitoring for local testing
            globalHeartbeatManager.startHeartbeat(
                sessionId,
                ws,
                30000, // 30 second ping interval
                10000, // 10 second pong timeout
                (sessionId, ws) => {
                    console.log(`💔 [${sessionId}] WebSocket heartbeat timeout in local testing - ending session`);
                    endSession(sessionId, { activeConnections, lifecycleManager }, 'heartbeat_timeout');
                }
            );

        } catch (error) {
            console.error(`❌ [${sessionId}] Error creating DIRECT Gemini session:`, error);
            geminiSession = null;
        }

        if (geminiSession) {
            // Initialize session lifecycle for local testing
            lifecycleManager.startSession(sessionId, connectionData, sessionConfig);
            console.log(`✅ [${sessionId}] Session lifecycle started for ${flowType} testing`);
            
            // Initialize Deepgram transcription for local testing
            try {
                const dgConnection = await deps.transcriptionManager.initializeLocalTranscription(sessionId, connectionData);
                if (dgConnection) {
                    connectionData.deepgramConnection = dgConnection;
                    console.log(`✅ [${sessionId}] Local Deepgram transcription initialized for ${flowType} testing`);
                }
            } catch (error) {
                console.warn(`⚠️ [${sessionId}] Failed to initialize local Deepgram transcription:`, error);
            }

            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-started',
                    sessionId: sessionId,
                    flowType: flowType,
                    scriptId: sessionConfig.scriptId,
                    config: {
                    voice: sessionConfig.voice,
                    model: sessionConfig.model,
                    isIncomingCall: isIncomingCall,
                    transcriptionEnabled: !!connectionData.deepgramConnection
                }
                }));
            }
            console.log(`✅ [${sessionId}] ${flowType.toUpperCase()} testing session started successfully`);
        } else {
            console.error(`❌ [${sessionId}] Gemini session creation returned null/undefined`);
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: 'Failed to create Gemini session'
                }));
            }
        }

        return { geminiSession, isSessionActive };

    } catch (startSessionError) {
        console.error(`❌ [${sessionId}] Critical error in start-session for ${flowType}:`, startSessionError);
        console.error(`❌ [${sessionId}] Error stack:`, startSessionError.stack);
        try {
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: `Session start failed: ${startSessionError.message}`
                }));
            }
        } catch (sendError) {
            console.error(`❌ [${sessionId}] Failed to send error message:`, sendError);
        }
        return { geminiSession: null, isSessionActive: false };
    }
}

async function handleAudioData(sessionId, data, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager, flowType) {
    if (geminiSession && isSessionActive && (data.audioData || data.audio)) {
        try {
            // Update activity for session persistence
            lifecycleManager.updateActivity(sessionId);

            // Handle browser audio data (support both audioData and audio fields)
            const base64Audio = data.audioData || data.audio;
            console.log(`🔍 [${sessionId}] About to call sendBrowserAudioToGemini - audioSize: ${base64Audio.length}, geminiSession exists: ${!!geminiSession}`);
            await deps.sessionManager.sendBrowserAudioToGemini(sessionId, geminiSession, base64Audio);

            // Send audio to Deepgram for transcription (local testing)
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && connectionData.deepgramConnection && deps.transcriptionManager) {
                try {
                    const audioBuffer = Buffer.from(base64Audio, 'base64');
                    deps.transcriptionManager.sendAudioToTranscription(sessionId, audioBuffer);
                } catch (dgError) {
                    console.warn(`⚠️ [${sessionId}] Local Deepgram send error:`, dgError);
                }
            }

        } catch (error) {
            console.error(`❌ [${sessionId}] Error processing ${flowType} testing audio:`, error);

            // Check if we need to recover the session
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(sessionId, activeConnections)) {
                console.log(`🔄 [${sessionId}] Audio processing failed in ${flowType} testing, attempting session recovery`);
                await recoveryManager.recoverSession(sessionId, 'audio_processing_error', activeConnections);
            }
        }
    }
}

async function handleTextMessage(sessionId, data, geminiSession, isSessionActive, deps) {
    if (geminiSession && isSessionActive && data.text) {
        try {
            console.log(`💬 [${sessionId}] Received text message: "${data.text}"`);
            await deps.sessionManager.sendTextToGemini(sessionId, geminiSession, data.text);
        } catch (error) {
            console.error(`❌ [${sessionId}] Error sending text to Gemini:`, error);
        }
    }
}

async function handleTurnComplete(sessionId, geminiSession, isSessionActive, deps) {
    if (geminiSession && isSessionActive) {
        try {
            console.log(`🔚 [${sessionId}] Received turn complete signal - telling AI to respond`);
            await deps.sessionManager.sendTurnComplete(sessionId, geminiSession);
        } catch (error) {
            console.error(`❌ [${sessionId}] Error sending turn complete:`, error);
        }
    }
}

async function handleEndSession(sessionId, deps, activeConnections, lifecycleManager) {
    console.log(`🔚 [${sessionId}] Ending testing session - USER INITIATED`);
    // This is a user-initiated session end for testing
    const endConnectionData = activeConnections.get(sessionId);
    if (endConnectionData) {
        // Clean up Deepgram transcription
        if (endConnectionData.deepgramConnection) {
            deps.transcriptionManager.closeTranscription(sessionId);
        }

        if (lifecycleManager) {
            await lifecycleManager.requestSessionEnd(sessionId, endConnectionData, 'user_end_testing');
        } else {
            await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
        }
    } else {
        await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
    }
}

async function handleRequestSummary(sessionId, _deps, activeConnections, summaryManager, contextManager) {
    console.log(`📝 [${sessionId}] Manual summary requested for testing`);
    const summaryConnectionData = activeConnections.get(sessionId);
    if (summaryConnectionData && summaryManager) {
        await summaryManager.requestSummary(sessionId, summaryConnectionData, contextManager);
    }
}
