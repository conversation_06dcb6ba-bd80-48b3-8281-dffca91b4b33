// Simple authentication middleware for Supabase integration
import { authLogger } from '../utils/logger.js';
import { timingSafeEqual } from 'crypto';

export async function validateSupabaseAuth(request, reply) {
    // Skip auth for WebSocket connections
    if (request.headers && request.headers.upgrade === 'websocket') {
        return;
    }

    // Skip auth for Twilio webhooks and health check endpoints
    const skipPaths = [
        '/incoming-call',    // Twilio webhook
        '/call-status',      // Twilio webhook
        '/recording-status', // Twilio webhook
        '/media-stream',     // Twilio WebSocket
        '/health',           // Health check
        '/static'            // Static files
    ];

    if (skipPaths.some(path => request.url.startsWith(path))) {
        return; // Continue without validation
    }
    
    const authHeader = request.headers?.authorization;
    
    if (!authHeader) {
        // Enforce authentication in production
        if (process.env.NODE_ENV === 'production') {
            authLogger.error('No authorization header in production', {
                url: request.url,
                method: request.method,
                ip: request.ip
            });
            reply.code(401).send({ error: 'Authorization required' });
            return;
        }
        // Allow in development and test environments with warning
        if (process.env.NODE_ENV === 'test') {
            authLogger.debug('No authorization header - allowing for test environment');
        } else {
            authLogger.warn('No authorization header - allowing for development only');
        }
        return;
    }

    try {
        // Extract token from "Bearer <token>" format
        const token = authHeader.replace('Bearer ', '');
        
        if (!token || token === 'undefined' || token === 'null') {
            throw new Error('Invalid token format');
        }
        
        // Simple token validation for now
        // In production, this should validate against Supabase or a JWT secret
        if (process.env.NODE_ENV === 'production') {
            // Validate token format (basic check)
            if (token.length < 32) {
                throw new Error('Token too short');
            }
            
            // Check against a configured API key as temporary measure
            const validApiKey = process.env.API_KEY || process.env.SUPABASE_SERVICE_KEY;
            if (validApiKey && !timingSafeEqual(Buffer.from(token), Buffer.from(validApiKey))) {
                throw new Error('Invalid API key');
            }
        }
        
        authLogger.info('Auth validation passed', {
            tokenLength: token.length,
            url: request.url
        });
        
        // Attach user info to request for downstream use
        request.auth = {
            token,
            authenticated: true
        };
        
    } catch (error) {
        authLogger.error('Auth validation failed', {
            error: error.message,
            url: request.url,
            method: request.method
        });
        reply.code(401).send({ error: 'Unauthorized' });
    }
}

// Middleware to require auth for specific routes
export function requireAuth(request, reply, done) {
    validateSupabaseAuth(request, reply)
        .then(() => done())
        .catch(() => {
            reply.code(401).send({ error: 'Authentication required' });
            done();
        });
}
