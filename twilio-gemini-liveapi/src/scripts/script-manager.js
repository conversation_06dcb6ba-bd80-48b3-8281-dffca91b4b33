// Script Management System for both incoming and outbound call scripts
// Now uses universal campaign script loader for real scripts from public directory
import { config, getConfigValue } from '../config/config.js';
import { ScriptCache } from './script-cache.js';
import { logger } from '../utils/logger.js';
import { convertIncomingScenarioToCampaignScript } from './incoming-converter.js';

// Import universal campaign script loader
import {
    loadCampaignScript,
    getAllCampaigns,
    formatCampaignScript,
    getIncomingCallScript as getOutboundCallScript,
    listIncomingCallScripts as listOutboundCallScripts,
    setIncomingCallScript as setOutboundCallScript,
    getCurrentIncomingScript as getCurrentOutboundScript,
    createCustomIncomingScript as createCustomOutboundScript,
    // Incoming scenario functions (now using real campaign scripts)
    getIncomingScenario,
    listIncomingScenarios,
    setActiveIncomingScenario,
    getCurrentIncomingScenario
} from '../../campaign-script-loader.js';

// All incoming scenarios now handled by universal campaign script loader
// No separate incoming system needed - everything uses real campaign scripts

export class ScriptManager {
    constructor() {
        this.currentIncomingScript = 'customer-service';
        this.currentOutboundScript = 'default';
        this.customScripts = new Map();
        this.scriptCache = new ScriptCache();
        this.isPreloading = false;
    }

    async preloadScripts() {
        if (this.isPreloading) return;
        this.isPreloading = true;
        const start = Date.now();
        try {
            for (let i = 1; i <= 6; i++) {
                const outbound = loadCampaignScript(i, 'outbound');
                if (outbound) {
                    this.scriptCache.set(`outbound-${i}`, outbound);
                }
                const incoming = loadCampaignScript(i, 'incoming');
                if (incoming) {
                    this.scriptCache.set(`incoming-${i}`, incoming);
                }
            }
            logger.info(`📋 Script preloading completed`, {
                count: this.scriptCache.cache.size,
                duration_ms: Date.now() - start
            });
        } catch (error) {
            logger.error('❌ Error preloading scripts', { error });
        } finally {
            this.isPreloading = false;
        }
    }

    getScriptFromCache(id, type = 'outbound') {
        return this.scriptCache.get(`${type}-${id}`);
    }

    // === INCOMING CALL SCRIPTS ===
    
    /**
     * Get all available incoming call scripts
     */
    getIncomingScripts() {
        try {
            const scenarios = listIncomingScenarios();
            return scenarios.map(scenario => ({
                id: scenario.id,
                name: scenario.name,
                description: scenario.description,
                type: 'incoming',
                category: scenario.category || 'support'
            }));
        } catch (error) {
            console.error('Error getting incoming scripts:', error);
            return [];
        }
    }

    /**
     * Get current active incoming script
     */
    getCurrentIncomingScript() {
        try {
            return getCurrentIncomingScenario();
        } catch (error) {
            console.error('Error getting current incoming script:', error);
            return null;
        }
    }

    /**
     * Set active incoming script
     */
    setIncomingScript(scriptId) {
        try {
            return setActiveIncomingScenario(scriptId);
        } catch (error) {
            console.error('Error setting incoming script:', error);
            return false;
        }
    }

    /**
     * Create custom incoming script
     */
    createCustomIncomingScript(scriptData) {
        try {
            // No custom scenarios - we use 6 outbound + 6 inbound ready-made scripts
            console.log('Custom incoming scripts not supported - use campaigns 1-6 (incoming)');
            return false;
        } catch (error) {
            console.error('Error creating custom incoming script:', error);
            return false;
        }
    }

    // === OUTBOUND CALL SCRIPTS ===
    
    /**
     * Get all available outbound call scripts
     */
    getOutboundScripts() {
        try {
            return listOutboundCallScripts();
        } catch (error) {
            console.error('Error getting outbound scripts:', error);
            return [];
        }
    }

    /**
     * Get current active outbound script
     */
    getCurrentOutboundScript() {
        try {
            return getCurrentOutboundScript();
        } catch (error) {
            console.error('Error getting current outbound script:', error);
            return null;
        }
    }

    /**
     * Set active outbound script
     */
    setOutboundScript(scriptId) {
        try {
            return setOutboundCallScript(scriptId);
        } catch (error) {
            console.error('Error setting outbound script:', error);
            return false;
        }
    }

    /**
     * Create custom outbound script
     */
    createCustomOutboundScript(scriptData) {
        try {
            return createCustomOutboundScript(scriptData);
        } catch (error) {
            console.error('Error creating custom outbound script:', error);
            return false;
        }
    }

    // === CAMPAIGN SCRIPT CONVERSION ===
    

    /**
     * Get script configuration for session
     */
    getScriptConfig(scriptId, isIncoming = false) {
        try {
            // UNIFIED SCRIPT LOADING - One simple approach
            const numericId = parseInt(scriptId);
            let campaignScript = null;
            let campaignId = numericId;
            let scriptType = isIncoming ? 'incoming' : 'outbound';

            // Handle unified ID system: 1-6 = outbound, 7-12 = incoming
            if (numericId >= 7 && numericId <= 12) {
                campaignId = numericId - 6; // 7->1, 8->2, ..., 12->6
                scriptType = 'incoming';
            } else if (numericId >= 1 && numericId <= 6) {
                campaignId = numericId;
                scriptType = 'outbound';
            } else {
                // For non-numeric IDs, use the isIncoming parameter
                campaignId = 1; // Default to campaign 1
            }

            // Try to get from cache first
            campaignScript = this.getScriptFromCache(campaignId, scriptType);

            // If not in cache, load it
            if (!campaignScript) {
                campaignScript = loadCampaignScript(campaignId, scriptType, false);
                if (campaignScript) {
                    this.scriptCache.set(`${scriptType}-${campaignId}`, campaignScript);
                }
            }

            // If we have a campaign script, return the config
            if (campaignScript) {
                return {
                    aiInstructions: this.formatCampaignInstructions(campaignScript),
                    voice: campaignScript.agentPersona?.voice || getConfigValue('ai.gemini.defaultVoice', 'Kore'),
                    model: campaignScript.agentPersona?.model || getConfigValue('ai.gemini.defaultModel', 'gemini-2.5-flash-preview-native-audio-dialog'),
                    isIncomingCall: scriptType === 'incoming',
                    scriptType: scriptType,
                    scriptId: scriptId,
                    campaignId: campaignId
                };
            }

            // If no script found, return null
            console.warn(`⚠️ No script found for ID: ${scriptId}, isIncoming: ${isIncoming}`);
            return null;
        } catch (error) {
            console.error('❌ Error in getScriptConfig:', error);
            return null;
        }
    }

    /**
     * Format campaign script into AI instructions
     */
    formatCampaignInstructions(campaignScript) {
        if (!campaignScript) return '';

        // Create comprehensive instructions from the campaign script
        let instructions = '';

        if (campaignScript.title) {
            instructions += `CAMPAIGN: ${campaignScript.title}\n\n`;
        }

        if (campaignScript.agentPersona) {
            instructions += `AGENT PERSONA:\n${JSON.stringify(campaignScript.agentPersona, null, 2)}\n\n`;
        }

        if (campaignScript.campaign) {
            instructions += `SCRIPT TO FOLLOW:\n${campaignScript.campaign}\n\n`;
        }

        if (campaignScript.objectives) {
            instructions += `OBJECTIVES:\n${campaignScript.objectives.join('\n')}\n\n`;
        }

        if (campaignScript.keyPoints) {
            instructions += `KEY POINTS:\n${campaignScript.keyPoints.join('\n')}\n\n`;
        }

        instructions += `\nIMPORTANT: Follow this campaign script exactly. Stay in character and achieve the campaign objectives.`;

        return instructions;
    }

    /**
     * Validate script data
     */
    validateScript(scriptData) {
        const required = ['id', 'name', 'description'];
        for (const field of required) {
            if (!scriptData[field]) {
                throw new Error(`Missing required field: ${field}`);
            }
        }
        return true;
    }
}

// Export singleton instance
export const scriptManager = new ScriptManager();
