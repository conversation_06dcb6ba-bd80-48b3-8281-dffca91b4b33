import { Modality } from '../gemini/client.js';
import { AudioProcessor } from '../audio/audio-processor.js';

// Bounded Map and Set for memory safety
class BoundedMap extends Map {
    constructor(maxSize = 1000) {
        super();
        this.maxSize = maxSize;
    }

    set(key, value) {
        if (this.size >= this.maxSize && !this.has(key)) {
            const firstKey = this.keys().next().value;
            this.delete(firstKey);
            console.log(`🧹 SessionManager BoundedMap: Removed oldest entry ${firstKey}`);
        }
        return super.set(key, value);
    }
}

class BoundedSet extends Set {
    constructor(maxSize = 1000) {
        super();
        this.maxSize = maxSize;
    }

    add(value) {
        if (this.size >= this.maxSize && !this.has(value)) {
            const firstValue = this.values().next().value;
            this.delete(firstValue);
            console.log(`🧹 SessionManager BoundedSet: Removed oldest entry ${firstValue}`);
        }
        return super.add(value);
    }
}

// Session Manager for Gemini connections with recovery
export class SessionManager {
    constructor(contextManager, geminiClient, activeConnections = null) {
        this.contextManager = contextManager;
        this.geminiClient = geminiClient;
        this.recoveryInProgress = new BoundedSet(200); // Limit recovery operations
        this.audioProcessor = new AudioProcessor();
        this.sessionMetrics = new BoundedMap(1000); // Limit session metrics
        this.activeConnections = activeConnections; // Reference to activeConnections for audio forwarding
    }

    // Create new Gemini session
    async createGeminiSession(callSid, config, connectionData) {
        try {
            console.log(`🤖 [${callSid}] Creating Gemini session with model: ${config.model}, voice: ${config.voice}`);
            console.log(`🔍 [${callSid}] ===== SESSION MANAGER MODEL DEBUG =====`);
            console.log(`🔍 [${callSid}] config.model = "${config.model}"`);
            console.log(`🔍 [${callSid}] config.voice = "${config.voice}"`);
            console.log(`🔍 [${callSid}] process.env.GEMINI_DEFAULT_MODEL = "${process.env.GEMINI_DEFAULT_MODEL}"`);
            console.log(`🔍 [${callSid}] ==========================================`);

            // Capture 'this' context for callbacks
            const self = this;

            // Initialize session metrics BEFORE creating session to avoid race conditions
            this.sessionMetrics.set(callSid, {
                startTime: Date.now(),
                messagesReceived: 0,
                messagesSent: 0,
                recoveryCount: 0,
                lastActivity: Date.now(),
                isInitializing: true
            });

            // Store reference immediately to prevent race conditions
            connectionData.geminiSession = null;
            connectionData.isSessionActive = false;

            // Create session and store reference immediately
            const geminiSession = await this.geminiClient.live.connect({
                model: config.model,
                callbacks: {
                    onopen: () => {
                        console.log(`✅ [${callSid}] Gemini session opened`);
                        connectionData.isSessionActive = true;

                        // Update metrics state
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.isInitializing = false;
                        }

                        // Save initial context with bounded arrays
                        this.contextManager.saveSessionContext(callSid, {
                            ...config,
                            ...connectionData,
                            conversationLog: [],
                            fullTranscript: [],
                            maxConversationLogSize: 500,
                            maxTranscriptSize: 1000
                        });

                        // AI instructions are sent after session creation, not here
                        // This prevents duplicate instructions being sent

                        console.log(`🔄 [${callSid}] Session initialized and ready for conversation`);
                    },

                    onerror: (error) => {
                        console.error(`❌ [${callSid}] Gemini session error:`, error);
                        this.contextManager.markSessionInterrupted(callSid, 'session_error');

                        // Update metrics
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.recoveryCount++;
                        }

                        // Mark session as inactive but don't end it - let recovery manager handle it
                        connectionData.isSessionActive = false;
                        connectionData.geminiSessionError = error.message;

                        console.log(`🔄 [${callSid}] Gemini session error detected, session marked for recovery`);
                        // Recovery will be handled by the recovery manager's health checks or explicit recovery calls
                    },

                    onclose: () => {
                        console.log(`🔌 [${callSid}] Gemini session closed`);
                        connectionData.isSessionActive = false;

                        // Session closed - cleanup handled by lifecycle manager

                        // Check if this is an unexpected close (connection still active)
                        const isUnexpectedClose = connectionData.twilioWs && connectionData.twilioWs.readyState === 1; // WebSocket.OPEN
                        const isLocalTestingActive = connectionData.localWs && connectionData.localWs.readyState === 1;

                        if (isUnexpectedClose || isLocalTestingActive) {
                            console.log(`⚠️ [${callSid}] Unexpected Gemini session close detected`);
                            this.contextManager.markSessionInterrupted(callSid, 'session_closed_unexpected');

                            // Session will be recovered by recovery manager's health checks or explicit recovery calls
                            console.log(`🔄 [${callSid}] Session marked for recovery due to unexpected close`);
                        } else {
                            console.log(`✅ [${callSid}] Gemini session closed normally (connection also closed)`);
                        }
                    },

                    onmessage: async (message) => {
                        try {
                            // Log ALL raw messages from Gemini first
                            console.log(`🔍 [${callSid}] RAW Gemini message received:`, {
                                hasMessage: !!message,
                                messageKeys: message ? Object.keys(message) : [],
                                hasServerContent: !!message?.serverContent,
                                hasSetupComplete: !!message?.setupComplete,
                                hasGoAway: !!message?.goAway,
                                sessionActive: connectionData?.isSessionActive
                            });

                            // Log Gemini API messages for debugging (excluding audio packets)
                            const hasAudio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
                            if (!hasAudio) {
                                console.log(`📨 [${callSid}] Gemini API message (non-audio):`, JSON.stringify(message, null, 2));
                            } else {
                                console.log(`🎵 [${callSid}] Gemini audio packet received (${message.serverContent?.modelTurn?.parts?.[0]?.inlineData?.data?.length || 0} bytes)`);
                            }

                            // Handle setup messages that don't have serverContent
                            if (message.setupComplete || message.goAway) {
                                console.log(`🔍 [${callSid}] Skipping setupComplete or goAway message`);
                                return;
                            }

                            // Enhanced message validation and handling
                            if (!message || !message.serverContent) {
                                console.warn(`⚠️ [${callSid}] Received invalid message structure`);
                                return;
                            }

                            // Update metrics with null check
                            const metrics = self?.sessionMetrics?.get(callSid);
                            if (metrics) {
                                metrics.messagesReceived = (metrics.messagesReceived || 0) + 1;
                                metrics.lastActivity = Date.now();
                            }

                            // Handle audio response from Gemini with validation
                            const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;

                            if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
                                // Enhanced debugging for audio path
                                console.log(`🎵 [${callSid}] Gemini sent AUDIO response:`, {
                                    mimeType: audio.mimeType,
                                    dataLength: audio.data?.length || 0,
                                    hasData: !!audio.data,
                                    twilioWsState: connectionData?.twilioWs?.readyState,
                                    sessionType: connectionData?.sessionType
                                });
                                
                                // Validate audio data before processing
                                if (!audio.data || audio.data.length === 0) {
                                    console.warn(`⚠️ [${callSid}] Received empty audio data from Gemini`);
                                    return;
                                }

                                // Send audio to Twilio if connection is active
                                if (connectionData?.twilioWs && connectionData.twilioWs.readyState === 1) { // WebSocket.OPEN
                                    try {
                                        console.log(`🔄 [${callSid}] Starting audio conversion for Twilio...`);
                                        console.log(`🔄 [${callSid}] Audio processor available: ${!!self?.audioProcessor}`);
                                        console.log(`🔄 [${callSid}] Input audio data: ${audio.data?.length || 0} chars`);

                                        // Convert Gemini PCM audio to μ-law format for Twilio
                                        const convertedAudio = self?.audioProcessor?.fallbackPCMToUlaw(audio.data);
                                        console.log(`🔄 [${callSid}] Audio conversion result: ${!!convertedAudio}, length: ${convertedAudio?.length || 0}`);
                                        if (convertedAudio) {
                                            // Initialize sequence number if not exists
                                            if (!connectionData.sequenceNumber) {
                                                connectionData.sequenceNumber = 0;
                                            }

                                            const audioDelta = {
                                                event: 'media',
                                                sequenceNumber: connectionData.sequenceNumber.toString(),
                                                streamSid: connectionData.streamSid,
                                                media: {
                                                    payload: convertedAudio
                                                }
                                            };

                                            // Increment sequence number for next packet
                                            connectionData.sequenceNumber++;

                                            console.log(`🔄 [${callSid}] Sending audio to Twilio WebSocket...`);
                                            connectionData.twilioWs.send(JSON.stringify(audioDelta));
                                            console.log(`🔊 [${callSid}] Sent converted audio to Twilio (${convertedAudio.length} chars, seq: ${audioDelta.sequenceNumber}) - FALLBACK MODE`);
                                        }
                                    } catch (audioError) {
                                        console.error(`❌ [${callSid}] Error sending audio to Twilio:`, audioError);
                                        console.error(`❌ [${callSid}] Audio error stack:`, audioError.stack);
                                        console.error(`❌ [${callSid}] WebSocket state:`, connectionData?.twilioWs?.readyState);
                                    }
                                }

                                // AUDIO FORWARDING: Handle local WebSocket forwarding for non-Twilio sessions
                                // Note: Twilio audio forwarding is already handled above (lines 184-213)
                                if (connectionData.sessionType === 'local_test' || connectionData.sessionType === 'local' || connectionData.isTestMode) {
                                    console.log(`🔧 [${callSid}] Skipping session manager audio forwarding for local testing session (handled by refactored handler)`);
                                } else if (!(connectionData.isTwilioCall || connectionData.sessionType === 'twilio_call')) {
                                    // For non-Twilio session types, try local WebSocket forwarding
                                    console.log(`🔍 [${callSid}] Audio forwarding debug: localWs=${!!connectionData.localWs}, readyState=${connectionData.localWs?.readyState}, audioDataLength=${audio.data.length}`);

                                    let localWs = connectionData.localWs;
                                    if (localWs && localWs.readyState === 1) { // WebSocket.OPEN
                                        try {
                                            localWs.send(JSON.stringify({
                                                type: 'audio',
                                                audio: audio.data  // Raw PCM data for browser playback
                                            }));
                                            console.log(`🔊 [${callSid}] Sent raw PCM audio to local WebSocket (${audio.data.length} chars)`);
                                        } catch (audioError) {
                                            console.error(`❌ [${callSid}] Error sending audio to local WebSocket:`, audioError);
                                        }
                                    } else {
                                        console.warn(`⚠️ [${callSid}] Cannot forward audio to local WebSocket: localWs=${!!localWs}, readyState=${localWs?.readyState}`);
                                    }
                                }
                            }

                            // Handle text response for summary collection and conversation logging
                            const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
                            if (text) {
                                console.log(`💬 [${callSid}] Gemini response: ${text.substring(0, 100)}...`);

                                // Update AI responsiveness tracking - CRITICAL FOR TURN MANAGEMENT
                                connectionData.lastAIResponse = Date.now();
                                connectionData.responseTimeouts = 0; // Reset timeout counter
                                connectionData.connectionQuality = 'good';

                                // Update metrics
                                if (metrics) {
                                    metrics.lastActivity = Date.now();
                                }

                                // Log conversation for recovery purposes - CRITICAL FOR CONTEXT PRESERVATION
                                if (connectionData.conversationLog) {
                                    // Implement bounded array to prevent memory leaks
                                    const MAX_CONVERSATION_LOG_SIZE = 500; // Limit conversation log entries
                                    
                                    connectionData.conversationLog.push({
                                        role: 'assistant',
                                        content: text,
                                        timestamp: Date.now(),
                                        messageId: `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
                                    });
                                    
                                    // Remove oldest entries if limit exceeded
                                    if (connectionData.conversationLog.length > MAX_CONVERSATION_LOG_SIZE) {
                                        const removed = connectionData.conversationLog.splice(0, connectionData.conversationLog.length - MAX_CONVERSATION_LOG_SIZE);
                                        console.log(`🧹 [${callSid}] Trimmed ${removed.length} old conversation entries`);
                                    }
                                }

                                // Handle summary collection if requested
                                if (connectionData.summaryRequested) {
                                    connectionData.summaryText += text;
                                }
                            }

                        } catch (error) {
                            console.error(`❌ [${callSid}] Error processing Gemini message:`, error);
                            console.error(`❌ [${callSid}] Error stack:`, error.stack);
                            console.error(`❌ [${callSid}] Error details:`, {
                                name: error.name,
                                message: error.message,
                                cause: error.cause
                            });
                            console.error(`❌ [${callSid}] Session state:`, {
                                isSessionActive: connectionData?.isSessionActive,
                                twilioWsState: connectionData?.twilioWs?.readyState,
                                localWsState: connectionData?.localWs?.readyState
                            });
                            // DO NOT end session on message processing errors - just log and continue
                        }
                    }
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192
            });

            // NOTE: AI instructions will be sent by the calling handler (twilio-flow-handler.js or local-testing-handler.js)
            // This prevents duplicate instruction sending which was causing early session termination
            console.log(`✅ [${callSid}] Gemini session created successfully - instructions will be sent by handler`);
            if (config.aiInstructions) {
                console.log(`📝 [${callSid}] AI instructions ready (${config.aiInstructions.length} chars) - will be sent by handler`);
            } else {
                console.warn(`⚠️ [${callSid}] No AI instructions configured - handler should provide them`);
            }

            // Store the session in connectionData immediately after creation
            // This prevents race conditions where callbacks might execute before assignment
            connectionData.geminiSession = geminiSession;

            return geminiSession;

        } catch (error) {
            console.error(`❌ [${callSid}] Error creating Gemini session:`, error);
            return null;
        }
    }

    // Note: Message handling is now done directly in the onmessage callback

    // Send initial message to AI
    async sendInitialMessage(geminiSession, aiInstructions) {
        try {
            if (geminiSession && aiInstructions) {
                await geminiSession.sendClientContent({
                    turns: [{
                        role: 'user',
                        parts: [{
                            text: aiInstructions
                        }]
                    }],
                    turnComplete: true
                });
                console.log('📤 Initial AI instructions sent');
            }
        } catch (error) {
            console.error('❌ Error sending initial message:', error);
        }
    }

    // Send text to Gemini session
    async sendTextToGemini(callSid, geminiSession, text) {
        if (!callSid || !geminiSession || !text) {
            console.warn(`⚠️ [${callSid}] Missing required parameters for sendTextToGemini`);
            return;
        }

        try {
            console.log(`🔍 [${callSid}] Sending text to Gemini: "${text}"`);
            await geminiSession.sendClientContent({
                turns: [{
                    role: 'user',
                    parts: [{
                        text: text
                    }]
                }],
                turnComplete: false
            });
            console.log(`✅ [${callSid}] Text sent to Gemini successfully`);
        } catch (error) {
            console.error(`❌ [${callSid}] Error sending text to Gemini:`, error);
        }
    }

    // Send turn complete signal to Gemini (user finished speaking)
    async sendTurnComplete(callSid, geminiSession) {
        try {
            console.log(`🔍 [${callSid}] Sending turn complete signal to Gemini...`);
            await geminiSession.sendClientContent({
                turns: [{
                    role: 'user',
                    parts: [{
                        text: '[Turn complete - user finished speaking]'
                    }]
                }],
                turnComplete: true
            });
            console.log(`✅ [${callSid}] Turn complete signal sent successfully`);
        } catch (error) {
            console.error(`❌ [${callSid}] Error sending turn complete:`, error);
        }
    }

    // Send audio to Gemini session (for Twilio calls with μ-law audio)
    async sendAudioToGemini(callSid, geminiSession, audioBuffer) {
        try {
            console.log(`🔍 [${callSid}] sendAudioToGemini called - geminiSession: ${!!geminiSession}, audioBuffer: ${!!audioBuffer}, audioSize: ${audioBuffer?.length || 0}`);

            if (!geminiSession || !audioBuffer) {
                console.log(`⚠️ [${callSid}] sendAudioToGemini early return - missing geminiSession or audioBuffer`);
                return;
            }

            console.log(`🔍 [${callSid}] Updating metrics...`);
            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.messagesSent = (metrics.messagesSent || 0) + 1;
                metrics.lastActivity = Date.now();
            }

            console.log(`🔍 [${callSid}] Converting audio format - audioProcessor exists: ${!!this.audioProcessor}`);
            // Convert Twilio audio to Gemini format
            const pcmBuffer = this.audioProcessor.convertUlawToPCM(audioBuffer);
            console.log(`🔍 [${callSid}] PCM conversion complete - buffer size: ${pcmBuffer.length}`);

            const float32Data = this.audioProcessor.pcmToFloat32Array(pcmBuffer);
            console.log(`🔍 [${callSid}] Float32 conversion complete - array length: ${float32Data.length}`);

            const audioBlob = this.audioProcessor.createGeminiAudioBlob(float32Data);
            console.log(`🔍 [${callSid}] Audio blob created - size: ${audioBlob.data?.length || 'N/A'}`);

            console.log(`🔍 [${callSid}] Sending audio to Gemini session...`);
            // Send to Gemini using sendRealtimeInput
            await geminiSession.sendRealtimeInput({
                media: {
                    mimeType: audioBlob.mimeType,
                    data: audioBlob.data
                }
            });
            console.log(`✅ [${callSid}] Audio sent to Gemini successfully`);

        } catch (error) {
            console.error(`❌ [${callSid}] Error sending audio to Gemini:`, error);
        }
    }

    // Send browser PCM audio to Gemini session (for local testing)
    async sendBrowserAudioToGemini(callSid, geminiSession, base64Audio) {
        try {
            console.log(`🔍 [${callSid}] sendBrowserAudioToGemini called - geminiSession: ${!!geminiSession}, audioSize: ${base64Audio?.length || 0}`);

            if (!geminiSession || !base64Audio) {
                console.log(`⚠️ [${callSid}] sendBrowserAudioToGemini early return - missing geminiSession or audio`);
                return;
            }

            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.messagesSent = (metrics.messagesSent || 0) + 1;
                metrics.lastActivity = Date.now();
            }

            // Send audio exactly as received
            console.log(`🎵 [${callSid}] Sending browser audio to Gemini...`);
            
            try {
                // Send audio - just pass the media object
                await geminiSession.sendRealtimeInput({
                    media: {
                        data: base64Audio,
                        mimeType: 'audio/pcm;rate=16000'  // Browser sends PCM16
                    }
                });
                console.log(`✅ [${callSid}] Browser audio sent to Gemini successfully`);
                
                // Add activity timestamp to track continuous flow
                const currentTime = Date.now();
                console.log(`⏱️ [${callSid}] Audio sent at: ${currentTime}, session active: ${!!geminiSession}`);
                
            } catch (sendError) {
                console.error(`🚨 [${callSid}] GEMINI AUDIO SEND ERROR 🚨`);
                console.error(`❌ [${callSid}] Error sending browser audio directly:`, sendError);
                console.error(`❌ [${callSid}] Error message: ${sendError?.message || 'No message'}`);
                
                // Check for quota errors
                if (sendError?.message?.includes('quota') || sendError?.message?.includes('exceeded')) {
                    console.error(`🚨🚨🚨 [${callSid}] QUOTA EXCEEDED IN AUDIO SEND 🚨🚨🚨`);
                    console.error(`💳 [${callSid}] Check your Gemini API billing and quota limits!`);
                }
                
                // Try with WebM mime type as fallback
                try {
                    await geminiSession.sendRealtimeInput({
                        media: {
                            data: base64Audio,
                            mimeType: 'audio/webm'
                        }
                    });
                    console.log(`⚠️ [${callSid}] Browser audio sent with WebM mime type (fallback)`);
                } catch (fallbackError) {
                    console.error(`🚨 [${callSid}] FALLBACK AUDIO SEND ALSO FAILED 🚨`);
                    console.error(`❌ [${callSid}] All audio sending methods failed:`, fallbackError);
                    console.error(`❌ [${callSid}] Fallback error message: ${fallbackError?.message || 'No message'}`);
                    
                    if (fallbackError?.message?.includes('quota') || fallbackError?.message?.includes('exceeded')) {
                        console.error(`🚨🚨🚨 [${callSid}] QUOTA EXCEEDED IN FALLBACK TOO 🚨🚨🚨`);
                    }
                }
            }

        } catch (error) {
            console.error(`❌ [${callSid}] Error sending browser audio to Gemini:`, error);
        }
    }

    // Recover session after interruption
    async recoverSession(callSid, reason) {
        // Atomic check and set to prevent concurrent recovery attempts
        const wasAlreadyRecovering = this.recoveryInProgress.has(callSid);
        if (wasAlreadyRecovering) {
            console.log(`⏳ [${callSid}] Recovery already in progress`);
            return;
        }
        
        // Immediately add to recovery set before any async operations
        this.recoveryInProgress.add(callSid);
        
        try {
            const context = this.contextManager.getSessionContext(callSid);
            if (!context || !this.contextManager.canRecover(callSid)) {
                console.log(`❌ [${callSid}] Cannot recover session`);
                return;
            }

            // Get connection data from active connections (would need to be passed in)
            // This is a simplified version - in practice you'd need access to activeConnections
            const recoveryCount = this.contextManager.incrementRecoveryAttempt(callSid);
            console.log(`🔄 [${callSid}] Attempting session recovery #${recoveryCount} (reason: ${reason})`);

            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.recoveryCount = (metrics.recoveryCount || 0) + 1;
                metrics.lastRecoveryTime = Date.now();
            }

            // The actual recovery would happen in the main connection handler
            // This method primarily handles the recovery logic and context preparation
            
            console.log(`✅ [${callSid}] Recovery preparation completed`);

        } catch (error) {
            console.error(`❌ [${callSid}] Error during session recovery:`, error);
        } finally {
            this.recoveryInProgress.delete(callSid);
        }
    }

    // Generate session summary
    async generateSummary(callSid, connectionData, summaryPrompt) {
        try {
            console.log(`📋 [${callSid}] Generating call summary`);
            
            if (!connectionData?.geminiSession) {
                console.warn(`⚠️ [${callSid}] No Gemini session for summary generation`);
                return;
            }

            connectionData.summaryRequested = true;
            connectionData.summaryText = '';

            // Send summary request
            await connectionData.geminiSession.sendClientContent({
                turns: [{
                    role: 'user',
                    parts: [{
                        text: summaryPrompt
                    }]
                }],
                turnComplete: true
            });

            // Summary will be collected in the onmessage callback
            return true;

        } catch (error) {
            console.error(`❌ [${callSid}] Error generating summary:`, error);
            return false;
        }
    }

    // Get session metrics
    getSessionMetrics(callSid) {
        return this.sessionMetrics.get(callSid) || null;
    }

    // Clean up session
    cleanupSession(callSid) {
        this.sessionMetrics.delete(callSid);
        this.recoveryInProgress.delete(callSid);
        console.log(`🧹 [${callSid}] Session manager cleanup completed`);
    }
}





import { Modality } from '../gemini/client.js';
import { endSession } from './session-utils.js';
import { globalHeartbeatManager } from './heartbeat-manager.js';
import { websocketLogger } from '../utils/logger.js';

/**
 * Common Gemini session creation for both Twilio and local testing
 */
export async function createCommonGeminiSession(sessionId, sessionConfig, deps, connectionData, ws, flowType, sessionType) {
    const correctModel = sessionConfig.model || deps.GEMINI_DEFAULT_MODEL;
    const correctVoice = sessionConfig.voice || deps.GEMINI_DEFAULT_VOICE;

    let geminiSession = null;
    let isSessionActive = false;

    try {
        geminiSession = await deps.sessionManager.geminiClient.live.connect({
            model: correctModel,
            callbacks: {
                onopen: () => {
                    isSessionActive = true;
                    // Update connection data with active status
                    const currentConnectionData = deps.activeConnections?.get(sessionId);
                    if (currentConnectionData) {
                        currentConnectionData.isSessionActive = true;
                    }
                },
                onmessage: (message) => handleCommonGeminiMessage(sessionId, message, deps, connectionData, sessionType),
                onerror: () => {
                    isSessionActive = false;
                    // Update connection data with inactive status
                    const currentConnectionData = deps.activeConnections?.get(sessionId);
                    if (currentConnectionData) {
                        currentConnectionData.isSessionActive = false;
                    }
                },
                onclose: () => {
                    isSessionActive = false;
                    // Update connection data with inactive status
                    const currentConnectionData = deps.activeConnections?.get(sessionId);
                    if (currentConnectionData) {
                        currentConnectionData.isSessionActive = false;
                    }
                }
            },
            config: {
                responseModalities: [Modality.AUDIO],
                speechConfig: {
                    voiceConfig: {
                        prebuiltVoiceConfig: {
                            voiceName: correctVoice
                        }
                    }
                }
            },
            temperature: 1.1,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 8192
        });

        // Start heartbeat monitoring
        const heartbeatInterval = sessionType === 'twilio_call' ? 60000 : 30000; // Longer for Twilio
        const heartbeatTimeout = sessionType === 'twilio_call' ? 30000 : 10000;
        
        globalHeartbeatManager.startHeartbeat(sessionId, ws, heartbeatInterval, heartbeatTimeout, () => {
            if (sessionType === 'twilio_call') {
                // For Twilio, don't immediately end session on heartbeat timeout
                websocketLogger.warn('Twilio WebSocket heartbeat timeout - connection may be stale', { callSid: sessionId });
                const connectionData = deps.activeConnections?.get(sessionId);
                if (connectionData) {
                    connectionData.heartbeatTimeout = true;
                    connectionData.lastHeartbeatTimeout = Date.now();
                }
            } else {
                // For local testing, end session on heartbeat timeout
                endSession(sessionId, { activeConnections: deps.activeConnections, lifecycleManager: deps.lifecycleManager }, 'heartbeat_timeout');
            }
        });

        // Send initial AI instructions
        if (sessionConfig.aiInstructions && geminiSession) {
            await sendInitialInstructions(sessionId, sessionConfig, geminiSession, flowType, sessionType);
        }

    } catch (error) {
        console.error(`❌ [${sessionId}] Failed to create Gemini session:`, error);
        geminiSession = null;
    }

    return { geminiSession, isSessionActive };
}

/**
 * Send initial AI instructions based on session type and flow
 */
async function sendInitialInstructions(sessionId, sessionConfig, geminiSession, flowType, sessionType) {
    try {
        let messageText = sessionConfig.aiInstructions;

        // Handle different flow types
        if (flowType === 'outbound_test' || flowType === 'outbound_call') {
            // Outbound: Add conversation trigger
            if (sessionType === 'local_test') {
                messageText = `${sessionConfig.aiInstructions}\n\nHello, call answered. Start speaking now.`;
            } else {
                messageText = `${sessionConfig.aiInstructions}\n\nThe call has been answered. Start the conversation immediately according to your instructions.`;
            }
        } else if (flowType === 'inbound_test' || flowType === 'inbound_call') {
            // Incoming: Provide default instructions if empty and add greeting trigger
            if (!messageText || messageText.trim() === '') {
                messageText = `You are a helpful customer service representative. You should greet the caller warmly and ask how you can help them today. Be professional, friendly, and helpful.`;
            }
            messageText = `${messageText}\n\nA customer has just called you. Greet them warmly and ask how you can help them today. Start speaking immediately.`;
        }

        await geminiSession.sendClientContent({
            turns: [{
                role: 'user',
                parts: [{
                    text: messageText
                }]
            }],
            turnComplete: true
        });

        console.log(`✅ [${sessionId}] Initial instructions sent for ${flowType}`);

    } catch (error) {
        console.error(`❌ [${sessionId}] Failed to send initial instructions:`, error);
    }
}

/**
 * Common Gemini message handler for both Twilio and local testing
 */
function handleCommonGeminiMessage(sessionId, message, deps, connectionData, sessionType) {
    console.log(`🔍 [${sessionId}] handleCommonGeminiMessage called - sessionType: ${sessionType}, message keys:`, Object.keys(message || {}));

    if (message.setupComplete || message.goAway) {
        console.log(`🔍 [${sessionId}] Skipping setupComplete or goAway message`);
        return;
    }

    const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
    const text = message.serverContent?.modelTurn?.parts?.[0]?.text;

    console.log(`🔍 [${sessionId}] Audio check: audio exists: ${!!audio}, data length: ${audio?.data?.length || 0}`);

    // Handle audio responses
    if (audio && audio.data && audio.data.length > 0) {
        handleAudioResponse(sessionId, audio, deps, connectionData, sessionType);
    }

    // Handle text responses
    if (text) {
        console.log(`💬 [${sessionId}] Text response received: ${text.substring(0, 100)}...`);
        const freshConnectionData = deps.activeConnections?.get(sessionId) || connectionData;
        if (freshConnectionData) {
            freshConnectionData.lastAIResponse = Date.now();
            freshConnectionData.responseTimeouts = 0;
            freshConnectionData.connectionQuality = 'good';
        }
    }
}

/**
 * Handle audio responses from Gemini - route to appropriate destination
 */
function handleAudioResponse(sessionId, audio, deps, connectionData, sessionType) {
    // Get fresh connection data to ensure we have the latest WebSocket state
    const freshConnectionData = deps.activeConnections?.get(sessionId) || connectionData;
    const localWs = freshConnectionData?.localWs;
    const twilioWs = freshConnectionData?.twilioWs;

    console.log(`🔍 [${sessionId}] Audio forwarding check: audio=${!!audio}, data=${audio.data?.length || 0}, sessionType=${sessionType}, localWs=${!!localWs}, twilioWs=${!!twilioWs}`);

    if (sessionType === 'local_test') {
        // Handle local testing sessions
        handleLocalAudioResponse(sessionId, audio, localWs);
    } else if (sessionType === 'twilio_call') {
        // Handle Twilio sessions (inbound/outbound calls)
        handleTwilioAudioResponse(sessionId, audio, twilioWs, freshConnectionData, deps);
    } else {
        console.warn(`⚠️ [${sessionId}] Unknown session type: ${sessionType}`);
    }
}

/**
 * Handle audio response for local testing
 */
function handleLocalAudioResponse(sessionId, audio, localWs) {
    if (localWs && localWs.readyState === 1) { // WebSocket.OPEN
        console.log(`🔊 [${sessionId}] Sending audio response to local client, size: ${audio.data?.length || 0}, mimeType: ${audio.mimeType}`);

        try {
            localWs.send(JSON.stringify({
                type: 'audio',
                audio: audio.data,
                mimeType: audio.mimeType
            }));
            console.log(`✅ [${sessionId}] Audio sent successfully to local WebSocket`);
        } catch (sendError) {
            console.error(`❌ [${sessionId}] Error sending audio to local WebSocket:`, sendError);
        }
    } else {
        console.warn(`⚠️ [${sessionId}] Cannot send audio to local WebSocket: readyState=${localWs?.readyState}`);
    }
}

/**
 * Handle audio response for Twilio calls
 */
function handleTwilioAudioResponse(sessionId, audio, twilioWs, connectionData, deps) {
    if (twilioWs && twilioWs.readyState === 1) { // WebSocket.OPEN
        console.log(`🔊 [${sessionId}] Sending audio response to Twilio, size: ${audio.data?.length || 0}, mimeType: ${audio.mimeType}`);

        try {
            // Convert Gemini PCM audio to μ-law format for Twilio
            const convertedAudio = deps.audioProcessor?.fallbackPCMToUlaw(audio.data);
            if (convertedAudio) {
                // Initialize sequence number if not exists
                if (!connectionData.sequenceNumber) {
                    connectionData.sequenceNumber = 0;
                }

                const audioDelta = {
                    event: 'media',
                    sequenceNumber: connectionData.sequenceNumber.toString(),
                    streamSid: connectionData.streamSid,
                    media: {
                        payload: convertedAudio
                    }
                };

                twilioWs.send(JSON.stringify(audioDelta));
                connectionData.sequenceNumber++;
                console.log(`✅ [${sessionId}] Audio sent successfully to Twilio WebSocket`);
            } else {
                console.error(`❌ [${sessionId}] Failed to convert audio for Twilio`);
            }
        } catch (sendError) {
            console.error(`❌ [${sessionId}] Error sending audio to Twilio WebSocket:`, sendError);
        }
    } else {
        console.warn(`⚠️ [${sessionId}] Cannot send audio to Twilio WebSocket: readyState=${twilioWs?.readyState}`);
    }
}

/**
 * Create connection data for local testing sessions
 */
export function createLocalConnectionData(sessionId, connection, sessionConfig, flowType, isIncomingCall) {
    return {
        localWs: connection.socket || connection,
        sessionId,
        isSessionActive: false,
        summaryRequested: false,
        summaryReceived: false,
        summaryText: '',
        conversationLog: [],
        fullTranscript: [],
        speechTranscript: [],
        isIncomingCall,
        sessionType: 'local_test',
        flowType,
        sessionStartTime: Date.now(),
        lastActivity: Date.now(),
        targetName: sessionConfig.targetName || 'Test Contact',
        targetPhoneNumber: sessionConfig.targetPhoneNumber || '+**********',
        originalAIInstructions: sessionConfig.aiInstructions,
        scriptId: sessionConfig.scriptId,
        isTestMode: true,
        lastAIResponse: Date.now(),
        responseTimeouts: 0,
        connectionQuality: 'good',
        lastContextSave: Date.now(),
        contextSaveInterval: null
    };
}

/**
 * Create connection data for Twilio call sessions
 */
export function createTwilioConnectionData(callSid, ws, streamSid, sessionConfig, flowType, isIncomingCall) {
    return {
        twilioWs: ws,
        callSid,
        streamSid: streamSid,
        isSessionActive: false,
        summaryRequested: false,
        summaryReceived: false,
        summaryText: '',
        conversationLog: [],
        fullTranscript: [],
        speechTranscript: [],
        isIncomingCall,
        sessionType: 'twilio_call',
        flowType,
        sessionStartTime: Date.now(),
        lastActivity: Date.now(),
        targetName: sessionConfig.targetName || 'Contact',
        targetPhoneNumber: sessionConfig.targetPhoneNumber || '+**********',
        originalAIInstructions: sessionConfig.aiInstructions,
        scriptId: sessionConfig.scriptId,
        isTwilioCall: true,
        sequenceNumber: 0
    };
}





import { createCommonGeminiSession, createLocalConnectionData } from './common-session-handler.js';

export async function handleStartSession(
    sessionId,
    data,
    deps,
    connection,
    ws,
    flowType,
    isIncomingCall,
    getSessionConfig,
    activeConnections,
    healthMonitor,
    lifecycleManager,
    sessionManager
) {
    try {
        let sessionConfig = getSessionConfig();

        if (data.aiInstructions) sessionConfig.aiInstructions = data.aiInstructions;
        if (data.voice) sessionConfig.voice = deps.voiceManager.getValidGeminiVoice(data.voice);
        if (data.model) sessionConfig.model = deps.modelManager.getValidGeminiModel(data.model);
        if (data.scriptId) {
            const testConfig = deps.scriptManager.getScriptConfig(data.scriptId, isIncomingCall);
            if (testConfig) {
                sessionConfig = {
                    ...testConfig,
                    aiInstructions: `[TESTING MODE] ${testConfig.aiInstructions}`,
                    isTestMode: true
                };
            }
        }

        const connectionData = createLocalConnectionData(sessionId, connection, sessionConfig, flowType, isIncomingCall);
        activeConnections.set(sessionId, connectionData);

        healthMonitor.trackConnection(sessionId, 'connected', {
            flowType,
            isTestMode: true,
            scriptId: sessionConfig.scriptId
        });

        const { geminiSession, isSessionActive } = await createCommonGeminiSession(
            sessionId,
            sessionConfig,
            deps,
            connectionData,
            ws,
            flowType,
            'local_test'
        );

        connectionData.geminiSession = geminiSession;

        if (geminiSession) {
            await initTranscription(sessionId, deps, connectionData);
            if (ws.readyState === 1) {
                ws.send(
                    JSON.stringify({
                        type: 'session-started',
                        sessionId,
                        flowType,
                        scriptId: sessionConfig.scriptId,
                        config: {
                            voice: sessionConfig.voice,
                            model: sessionConfig.model,
                            isIncomingCall,
                            transcriptionEnabled: !!connectionData.deepgramConnection
                        }
                    })
                );
            }
        }

        return { geminiSession, isSessionActive };
    } catch (error) {
        if (ws.readyState === 1) {
            ws.send(
                JSON.stringify({ type: 'session-error', error: `Session start failed: ${error.message}` })
            );
        }
        return { geminiSession: null, isSessionActive: false };
    }
}



async function initTranscription(sessionId, deps, connectionData) {
    try {
        const dgConnection = await deps.transcriptionManager.initializeLocalTranscription(sessionId, connectionData);
        if (dgConnection) {
            connectionData.deepgramConnection = dgConnection;
        }
    } catch {}
}





import { endSession } from './session-utils.js';
import { websocketLogger } from '../utils/logger.js';
import { globalHeartbeatManager } from './heartbeat-manager.js';
import { createTwilioConnectionData } from './common-session-handler.js';
import { handleTurnComplete } from './audio-handlers.js';

// Twilio flow handler for both inbound and outbound calls
export function handleTwilioFlow(connection, deps) {
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        summaryManager,
        lifecycleManager,
        recoveryManager,
        transcriptionManager,
        flowType,
        getSessionConfig,
        isIncomingCall
    } = deps;

    const callSid = connection.query?.CallSid || `twilio-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    websocketLogger.info(`Twilio ${flowType} call started`, { callSid, flowType, isIncomingCall });

    const ws = connection.socket || connection;

    // CRITICAL FIX: Don't use persistent session variables - always get fresh state from activeConnections
    // This prevents stale state issues between multiple calls
    console.log(`🔄 [${callSid}] Starting fresh Twilio flow handler`);

    // Store event listeners for cleanup
    const eventListeners = new Map();

    const messageHandler = async (message) => {
        try {
            const data = JSON.parse(message);

            // Twilio uses 'event' field for message type
            const messageType = data.event;
            websocketLogger.debug(`Twilio ${flowType} message received`, { callSid, event: data.event, messageType });

            switch (messageType) {
                case 'connected':
                    // Twilio sends 'connected' event when WebSocket connection is established
                    console.log(`🔌 [${callSid}] Twilio CONNECTED event received:`, JSON.stringify(data, null, 2));
                    websocketLogger.info('Twilio WebSocket connected', { callSid });
                    break;

                case 'start':
                    // Twilio sends 'start' event when stream begins
                    console.log(`🔍 [${callSid}] Twilio START event received:`, JSON.stringify(data, null, 2));
                    await handleTwilioStartSession(callSid, data, deps, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager);
                    break;

                case 'media':
                    // Always get fresh session state from activeConnections
                    const connectionData = activeConnections.get(callSid);
                    const geminiSession = connectionData?.geminiSession;
                    const isSessionActive = connectionData?.isSessionActive;

                    await handleTwilioMedia(callSid, data, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager);
                    break;

                case 'mark':
                    websocketLogger.debug('Mark received', { callSid });
                    break;

                case 'stop':
                    websocketLogger.info('Stop message received from Twilio', { callSid });
                    await handleTwilioEndSession(callSid, deps, activeConnections, lifecycleManager);
                    break;

                default:
                    websocketLogger.warn(`Unknown Twilio message event: ${messageType}`, { callSid });
                    console.log(`🔍 [${callSid}] Unknown message:`, JSON.stringify(data, null, 2));
            }
        } catch (error) {
            websocketLogger.error(`Error processing Twilio ${flowType} message`, error, { callSid });
        }
    };

    // Store handlers for cleanup
    eventListeners.set('message', messageHandler);
    ws.on('message', messageHandler);

    const closeHandler = async (code, reason) => {
        websocketLogger.info(`Twilio ${flowType} connection closed`, {
            callSid,
            code,
            reason: reason || 'No reason',
            closeCode: code
        });

        // Stop heartbeat monitoring
        globalHeartbeatManager.stopHeartbeat(callSid);

        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // CRITICAL FIX: Don't end session immediately on WebSocket close
            // Twilio WebSocket can close/reconnect during normal call flow
            // Only end session if this is a deliberate call termination

            // Check if this is a normal close (1000) or abnormal close
            const isNormalClose = code === 1000;
            const isCallStillActive = connectionData.isSessionActive && connectionData.geminiSession;

            if (isNormalClose || !isCallStillActive) {
                // Normal close or session already inactive - safe to end
                websocketLogger.info(`Ending session due to normal WebSocket close or inactive session`, { callSid, code, isCallStillActive });
                if (lifecycleManager) {
                    await lifecycleManager.requestSessionEnd(callSid, connectionData, 'twilio_connection_closed');
                } else {
                    endSession(callSid, deps, 'twilio_connection_closed');
                }
            } else {
                // Abnormal close but session still active - mark for potential recovery
                websocketLogger.warn(`WebSocket closed abnormally but session still active - marking for recovery`, { callSid, code });
                connectionData.twilioWsDisconnected = true;
                connectionData.lastDisconnectTime = Date.now();

                // Don't end session immediately - let recovery manager handle it
                // or wait for explicit call termination
            }
        }

        // Clean up event listeners
        for (const [event, handler] of eventListeners) {
            ws.removeListener(event, handler);
        }
        eventListeners.clear();

        // CRITICAL: Clean up connection data to prevent stale state
        console.log(`🧹 [${callSid}] Cleaning up connection data to prevent stale state`);
        activeConnections.delete(callSid);
    };

    const errorHandler = async (error) => {
        websocketLogger.error(`Twilio ${flowType} error`, error, { callSid });

        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // Try recovery first if possible
            if (recoveryManager && contextManager.canRecover(callSid)) {
                websocketLogger.info('Twilio WebSocket error detected, attempting session recovery', { callSid });
                contextManager.markSessionInterrupted(callSid, 'twilio_websocket_error');
                setTimeout(async () => {
                    await recoveryManager.recoverSession(callSid, 'twilio_websocket_error', activeConnections);
                }, 1000);
            } else {
                // Mark connection as errored but don't immediately end session
                // Let the call status webhook or explicit user action end the session
                websocketLogger.warn('WebSocket error but no recovery available - marking connection as errored', { callSid });
                connectionData.twilioWsError = true;
                connectionData.lastErrorTime = Date.now();

                // Clean up Deepgram transcription
                if (connectionData.deepgramConnection) {
                    deps.transcriptionManager.closeTranscription(callSid);
                }

                // Only end session if this is a critical error and session is not active
                if (!connectionData.isSessionActive || !connectionData.geminiSession) {
                    endSession(callSid, { ...deps, transcriptionManager: deps.transcriptionManager }, 'twilio_connection_error');
                }
            }
        }
    };

    // Register all event listeners
    eventListeners.set('close', closeHandler);
    eventListeners.set('error', errorHandler);
    ws.on('close', closeHandler);
    ws.on('error', errorHandler);
}

async function handleTwilioStartSession(callSid, data, deps, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager) {
    websocketLogger.info(`Starting Twilio ${flowType} session`, { callSid });

    try {
        // Get session config from the stored configuration (set during webhook call)
        let sessionConfig = getSessionConfig();

        // Extract Twilio stream information
        const streamSid = data.start?.streamSid;
        const accountSid = data.start?.accountSid;
        const twilioCallSid = data.start?.callSid;

        console.log(`🔍 [${callSid}] Twilio stream info:`, {
            streamSid,
            accountSid,
            twilioCallSid,
            configFound: !!sessionConfig,
            hasInstructions: !!sessionConfig?.aiInstructions
        });

        // Ensure we have a valid session config
        if (!sessionConfig || !sessionConfig.aiInstructions) {
            console.warn(`⚠️ [${callSid}] No session config found, using fallback`);
            // Try to get current script as fallback
            try {
                const currentScript = isIncomingCall
                    ? deps.scriptManager.getCurrentIncomingScript()
                    : deps.scriptManager.getCurrentOutboundScript();

                if (currentScript) {
                    sessionConfig = deps.scriptManager.getScriptConfig(currentScript.id, isIncomingCall);
                    console.log(`✅ [${callSid}] Using fallback script: ${currentScript.id}`);
                }
            } catch (error) {
                console.error(`❌ [${callSid}] Error getting fallback script:`, error);
            }
        }

        // Store enhanced connection data using common handler
        const connectionData = createTwilioConnectionData(callSid, ws, streamSid, sessionConfig, flowType, isIncomingCall);
        activeConnections.set(callSid, connectionData);

        // Track connection health
        healthMonitor.trackConnection(callSid, 'connected', {
            flowType,
            isTwilioCall: true,
            scriptId: sessionConfig.scriptId
        });

        // Create Gemini session using session manager (existing method)
        const geminiSession = await sessionManager.createGeminiSession(callSid, sessionConfig, connectionData);

        if (geminiSession) {
            connectionData.geminiSession = geminiSession;
            console.log(`✅ [${callSid}] Gemini session created and configured`);

            // Start lifecycle management for the session
            if (lifecycleManager) {
                lifecycleManager.startSession(callSid, connectionData, sessionConfig);
                console.log(`✅ [${callSid}] Lifecycle management started`);
            }

            // Start WebSocket heartbeat monitoring for Twilio
            globalHeartbeatManager.startHeartbeat(
                callSid,
                ws,
                60000, // 60 second ping interval (longer for Twilio)
                30000, // 30 second pong timeout (longer for Twilio)
                () => {
                    websocketLogger.warn('Twilio WebSocket heartbeat timeout - connection may be stale', { callSid });
                    const connectionData = activeConnections.get(callSid);
                    if (connectionData) {
                        connectionData.heartbeatTimeout = true;
                        connectionData.lastHeartbeatTimeout = Date.now();
                        websocketLogger.info('Marked Twilio connection as having heartbeat timeout', { callSid });
                    }
                }
            );

            // Send initial AI instructions (required for AI to start responding)
            console.log(`🔍 [${callSid}] Instruction check: hasInstructions=${!!sessionConfig.aiInstructions}, hasSession=${!!geminiSession}, instructionLength=${sessionConfig.aiInstructions?.length || 0}`);
            if (sessionConfig.aiInstructions && geminiSession) {
                try {
                    websocketLogger.info(`Sending ${flowType} instructions to Gemini`, { callSid });

                    // Handle instructions differently for incoming vs outbound calls
                    let messageText = sessionConfig.aiInstructions;

                    if (!isIncomingCall) {
                        // Outbound calls: Add conversation trigger
                        websocketLogger.info(`Combining instructions with conversation trigger for outbound call`, { callSid });
                        messageText = `${sessionConfig.aiInstructions}\n\nThe call has been answered. Start the conversation immediately according to your instructions.`;
                    } else {
                        // Incoming calls: Provide default instructions if empty and add greeting trigger
                        if (!messageText || messageText.trim() === '') {
                            websocketLogger.warn(`No AI instructions found for incoming call, using default`, { callSid });
                            messageText = `You are a helpful customer service representative. You should greet the caller warmly and ask how you can help them today. Be professional, friendly, and helpful.`;
                        }
                        websocketLogger.info(`Adding greeting trigger for incoming call`, { callSid });
                        messageText = `${messageText}\n\nA customer has just called you. Greet them warmly and ask how you can help them today. Start speaking immediately.`;
                    }

                    await geminiSession.sendClientContent({
                        turns: [{
                            role: 'user',
                            parts: [{
                                text: messageText
                            }]
                        }],
                        turnComplete: true
                    });

                    websocketLogger.info(`${flowType} instructions sent successfully`, { callSid });

                } catch (error) {
                    websocketLogger.error(`Failed to send initial AI instructions`, error, { callSid });
                }
            }

            ws.send(JSON.stringify({
                type: 'session-started',
                callSid,
                flowType,
                scriptId: sessionConfig.scriptId
            }));
            
            websocketLogger.info(`Twilio ${flowType} session started successfully`, { callSid });
        } else {
            // Gemini session failed to initialize
            websocketLogger.error(`Failed to create Gemini session for ${flowType} call`, { callSid });
            ws.send(JSON.stringify({
                type: 'session-error',
                error: 'Failed to initialize AI session. Please try again later.',
                critical: true
            }));
            
            // For Twilio calls, we should end the call gracefully
            if (deps.twilioHelper) {
                try {
                    await deps.twilioHelper.endCallWithMessage(callSid, 'We apologize, but we are unable to process your call at this time. Please try again later.');
                } catch (err) {
                    websocketLogger.error('Failed to end call with error message', err, { callSid });
                }
            }
            
            // Clean up and close connection
            endSession(callSid, deps, 'gemini_session_failed');
            ws.close();
            return;
        }

    } catch (error) {
        websocketLogger.error(`Error starting Twilio ${flowType} session`, error, { callSid });
        ws.send(JSON.stringify({
            type: 'session-error',
            error: `Session start failed: ${error.message}`
        }));
    }
}

async function handleTwilioMedia(callSid, data, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager) {
    if (data.media && data.media.payload) {
        try {
            // Update activity for session persistence
            lifecycleManager.updateActivity(callSid);

            // Get fresh connection data to check current session state
            const connectionData = activeConnections.get(callSid);
            const currentIsSessionActive = connectionData?.isSessionActive || isSessionActive;
            const currentGeminiSession = connectionData?.geminiSession || geminiSession;

            // Convert Twilio μ-law audio to PCM
            const ulawAudio = Buffer.from(data.media.payload, 'base64');

            // Send to Gemini only if session is truly active
            if (deps.sessionManager && currentGeminiSession && currentIsSessionActive) {
                console.log(`🔍 [${callSid}] Sending audio to Gemini - session active: ${currentIsSessionActive}`);
                await deps.sessionManager.sendAudioToGemini(callSid, currentGeminiSession, ulawAudio);
            } else {
                console.log(`⚠️ [${callSid}] Skipping audio send - session not ready (active: ${currentIsSessionActive}, session: ${!!currentGeminiSession})`);
            }

        } catch (error) {
            websocketLogger.error(`Error processing Twilio media`, error, { callSid });
            
            // Attempt recovery if needed
            const connectionData = activeConnections.get(callSid);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(callSid, activeConnections)) {
                await recoveryManager.recoverSession(callSid, 'media_processing_error', activeConnections);
            }
        }
    }
}

async function handleTwilioEndSession(callSid, deps, activeConnections, lifecycleManager) {
    websocketLogger.info('Ending Twilio session - stop received', { callSid });
    
    const connectionData = activeConnections.get(callSid);
    if (connectionData) {
        if (lifecycleManager) {
            await lifecycleManager.requestSessionEnd(callSid, connectionData, 'twilio_stop_received');
        } else {
            endSession(callSid, deps, 'twilio_stop_received');
        }
    }
}
