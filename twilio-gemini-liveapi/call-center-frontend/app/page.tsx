'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { PlusIcon, TrashIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import { toast, Toaster } from 'react-hot-toast';
import { CallResult } from './components/CallResult';
import { useTheme } from './components/ThemeProvider';
import ThemeToggle from './components/ThemeToggle';


import DeEssingToggle from './components/DeEssingToggle';
import { authenticatedFetch } from './utils/auth';

import { cn } from '../lib/utils';

interface Company {
  name: string;
  phone: string;
  status?: 'pending' | 'in-progress' | 'polling-results' | 'completed' | 'failed';
  callSid?: string;
}

interface DisplayCallResult {
  call_summary: string;
  customer_sentiment: string;
  callSid: string;
  targetPhone?: string;
  targetName?: string;
  targetPhoneNumber?: string;
  status?: string;
  timestamp?: string;
  recordingUrl?: string;
  recordingTimestamp?: string;
  duration?: string;
  callQuality?: string;
}

interface VoiceDetails {
  name: string;
  gender: string;
  characteristics: string;
  pitch?: string;
  timbre?: string;
  persona?: string;
}

interface CampaignData {
    campaign: string;
    agentPersona: object;
    customerData: object;
    transferData: object;
    script: object;
}

type CallStatus = 'idle' | 'loading-script' | 'configuring' | 'initiating-call' | 'in-progress' | 'polling-results' | 'completed' | 'failed';

// Gemini voices will be loaded from backend
const DEFAULT_GEMINI_VOICES = ["Orus", "Puck", "Charon", "Kore", "Fenrir", "Aoede"];
const DEFAULT_GEMINI_MODELS = {
  'gemini-2.5-flash-preview-native-audio-dialog': 'Gemini 2.5 Flash Preview',
  'gemini-2.0-flash': 'Gemini 2.0 Flash (GA)'
};
const AVAILABLE_LANGUAGES = [ // Define available languages
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Spanish' },
    { code: 'cz', name: 'Czech' },
    // Add more languages here as needed
];

const AVAILABLE_COUNTRIES = [
    { 
        code: 'US', 
        name: 'United States', 
        flag: '🇺🇸',
        phoneNumber: process.env.NEXT_PUBLIC_TWILIO_FROM_NUMBER_US || '+18455954168'
    },
    { 
        code: 'CZ', 
        name: 'Czech Republic', 
        flag: '🇨🇿',
        phoneNumber: process.env.NEXT_PUBLIC_TWILIO_FROM_NUMBER_CZ || '+************'
    },
    { 
        code: 'ES', 
        name: 'Spain', 
        flag: '🇪🇸',
        phoneNumber: process.env.NEXT_PUBLIC_TWILIO_FROM_NUMBER_ES || '+34123456789'
    }
];

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'https://gemini-api.verduona.com';
const DEFAULT_FROM_NUMBER = process.env.NEXT_PUBLIC_TWILIO_FROM_NUMBER || '+************';
const LOCAL_STORAGE_KEY_TARGET_COMPANIES = 'aiCallerTargetCompanies';

export default function Home() {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'outbound' | 'incoming'>('outbound');
  const [audioMode, setAudioMode] = useState<'twilio' | 'local'>('twilio'); // New state for audio routing
  const [incomingCallsEnabled, setIncomingCallsEnabled] = useState(false); // New state for incoming calls toggle

  const [companies, setCompanies] = useState<Company[]>([{ name: '', phone: '' }]);
  const [task, setTask] = useState('');
  const [fromPhoneNumber, setFromPhoneNumber] = useState(DEFAULT_FROM_NUMBER);
  const [availableVoices, setAvailableVoices] = useState<string[]>(DEFAULT_GEMINI_VOICES);
  const [voiceDetails, setVoiceDetails] = useState<Record<string, VoiceDetails>>({});
  const [selectedVoice, setSelectedVoice] = useState('Kore');
  const [voiceDescriptions, setVoiceDescriptions] = useState<Record<string, string>>({});
  const [availableModels, setAvailableModels] = useState<Record<string, unknown>>(DEFAULT_GEMINI_MODELS);
  const [selectedModel, setSelectedModel] = useState('gemini-2.5-flash-preview-native-audio-dialog');
  const [selectedLanguage, setSelectedLanguage] = useState('en'); // State for language
  const [selectedCountry, setSelectedCountry] = useState('CZ'); // State for country selection - default to Czech
  const [isProcessing, setIsProcessing] = useState(false); // Global lock for API calls
  const [isLoadingScript, setIsLoadingScript] = useState(false);
  const [latestSummary, setLatestSummary] = useState<DisplayCallResult | null>(null);
  const [currentCallSid, setCurrentCallSid] = useState<string | null>(null); // Still needed for active call tracking
  const [callStatus, setCallStatus] = useState<CallStatus>('idle');


  // Local audio testing state
  const [isRecording, setIsRecording] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [audioStatus, setAudioStatus] = useState('Ready to start local testing');
  const [aiInitTimer, setAiInitTimer] = useState(0); // Timer for AI initialization countdown
  const [isTerminating, setIsTerminating] = useState(false); // Visual indicator for session termination

  // Audio context and nodes refs
  const inputAudioContextRef = useRef<AudioContext | null>(null);
  const outputAudioContextRef = useRef<AudioContext | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const workletNodeRef = useRef<AudioWorkletNode | null>(null);
  const gainNodeRef = useRef<GainNode | null>(null);
  const sessionRef = useRef<{ sendRealtimeInput: (data: { media: { data: string; mimeType: string } }) => void; close: () => void } | null>(null);
  const audioSourcesRef = useRef<Set<AudioBufferSourceNode>>(new Set());
  const nextStartTimeRef = useRef<number>(0);

  // Fetch available voices and models from backend on component mount
  useEffect(() => {
    const fetchVoices = async () => {
      try {
        const response = await authenticatedFetch(`${BACKEND_URL}/available-voices`);
        if (response.ok) {
          const voiceData = await response.json();
          const voices = Object.keys(voiceData.voices || {});
          if (voices.length > 0) {
            setAvailableVoices(voices);
            setVoiceDetails(voiceData.voices || {});
            // Set default voice if current selection is not available
            if (!voices.includes(selectedVoice)) {
              setSelectedVoice(voiceData.currentDefault || voices[0]);
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch available voices:', error);
        // Keep using default voices if fetch fails
      }
    };

    const fetchModels = async () => {
      try {
        const response = await authenticatedFetch(`${BACKEND_URL}/available-models`);
        if (response.ok) {
          const modelData = await response.json();
          if (modelData.availableModels) {
            setAvailableModels(modelData.availableModels);
            // Set default model if current selection is not available
            if (!modelData.availableModels[selectedModel]) {
              setSelectedModel(modelData.defaultModel || 'gemini-2.5-flash-preview-native-audio-dialog');
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch available models:', error);
        // Keep using default models if fetch fails
      }
    };

    fetchVoices();
    fetchModels();
    loadVoiceConfiguration();
  }, [selectedVoice, selectedModel]); // Overall status

  const loadVoiceConfiguration = async () => {
    try {
      const response = await authenticatedFetch(`${BACKEND_URL}/api/voice-config`);
      if (response.ok) {
        const voiceConfig = await response.json();
        setVoiceDescriptions(voiceConfig.voiceDescriptions || {});
      }
    } catch (error) {
      console.error('Failed to load voice configuration:', error);
    }
  };
  const [lastError, setLastError] = useState<string | null>(null);

  const isInitialMount = useRef(true);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Load 'From Phone Number' based on selected country
  useEffect(() => {
    const selectedCountryData = AVAILABLE_COUNTRIES.find(c => c.code === selectedCountry);
    if (selectedCountryData) {
      setFromPhoneNumber(selectedCountryData.phoneNumber);
    } else {
      setFromPhoneNumber(DEFAULT_FROM_NUMBER);
    }
  }, [selectedCountry]);

  // Load Target Companies from local storage
  useEffect(() => {
    const storedCompanies = localStorage.getItem(LOCAL_STORAGE_KEY_TARGET_COMPANIES);
    if (storedCompanies) {
      try {
        const parsedCompanies: Company[] = JSON.parse(storedCompanies);
        if (Array.isArray(parsedCompanies) && parsedCompanies.length > 0) {
          setCompanies(parsedCompanies.map(c => ({ name: c.name || '', phone: c.phone || '' })));
        } else {
           setCompanies([{ name: '', phone: '' }]);
        }
      } catch (error) {
        console.error("Failed to parse stored target companies:", error);
        setCompanies([{ name: '', phone: '' }]);
      }
    } else {
        setCompanies([{ name: '', phone: '' }]);
    }
    isInitialMount.current = false;
  }, []);

  // Save Target Companies to local storage
  useEffect(() => {
    if (isInitialMount.current) return;
    const companiesToSave = companies.map(c => ({ name: c.name, phone: c.phone }));
    localStorage.setItem(LOCAL_STORAGE_KEY_TARGET_COMPANIES, JSON.stringify(companiesToSave));
  }, [companies]);

  // Cleanup polling interval
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
      }
    };
  }, []);

  // Initialize audio contexts only when needed (after user gesture)
  const initializeAudioContexts = useCallback(() => {
    // Check if contexts exist and are not closed
    const inputContextValid = inputAudioContextRef.current && inputAudioContextRef.current.state !== 'closed';
    const outputContextValid = outputAudioContextRef.current && outputAudioContextRef.current.state !== 'closed';

    if (inputContextValid && outputContextValid) {
      console.log('🔊 Audio contexts already initialized and valid');
      return;
    }

    try {
      const AudioContextClass = window.AudioContext || (window as unknown as { webkitAudioContext: typeof AudioContext }).webkitAudioContext;
      if (!AudioContextClass) {
        console.warn('AudioContext not supported');
        return;
      }

      // Create new input context if needed
      if (!inputContextValid) {
        inputAudioContextRef.current = new AudioContextClass({ sampleRate: 16000 });
        console.log('🔊 Input audio context created');
      }

      // Create new output context if needed
      if (!outputContextValid) {
        outputAudioContextRef.current = new AudioContextClass({ sampleRate: 24000 });
        console.log('🔊 Output audio context created');
      }

      if (outputAudioContextRef.current) {
        gainNodeRef.current = outputAudioContextRef.current.createGain();
        gainNodeRef.current.connect(outputAudioContextRef.current.destination);
        gainNodeRef.current.gain.value = 1.0; // Full volume
        console.log('🔊 Gain node created with volume:', gainNodeRef.current.gain.value);

        console.log('🔊 Audio contexts initialized:', {
          inputState: inputAudioContextRef.current?.state,
          outputState: outputAudioContextRef.current.state,
          inputSampleRate: inputAudioContextRef.current?.sampleRate,
          outputSampleRate: outputAudioContextRef.current.sampleRate,
          isInIframe: window !== window.top
        });
      }
    } catch (error) {
      console.error('❌ Error initializing audio contexts:', error);
    }
  }, []);

  // Check iframe context on mount (without creating audio contexts)
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Check iframe context and audio permissions
    console.log('🖼️ Iframe context check:', {
      isInIframe: window !== window.top,
      userAgent: navigator.userAgent,
      audioContextSupport: !!window.AudioContext,
      location: window.location.href
    });

    return () => {
      cleanupAudio();
    };
  }, []);

  const cleanupAudio = useCallback(() => {
    console.log('🧹 Starting comprehensive audio cleanup...');

    // Reset state flags immediately
    setIsRecording(false);
    setIsConnected(false);

    // Stop media stream tracks
    if (mediaStreamRef.current) {
      console.log('🎤 Stopping media stream tracks...');
      mediaStreamRef.current.getTracks().forEach(track => {
        track.stop();
        console.log(`🔇 Stopped ${track.kind} track`);
      });
      mediaStreamRef.current = null;
    }

    // Disconnect audio nodes
    if (sourceNodeRef.current) {
      console.log('🔌 Disconnecting source node...');
      try {
        sourceNodeRef.current.disconnect();
      } catch (error) {
        console.warn('⚠️ Error disconnecting source node:', error);
      }
      sourceNodeRef.current = null;
    }

    if (workletNodeRef.current) {
      console.log('🔌 Disconnecting worklet node...');
      try {
        workletNodeRef.current.disconnect();
      } catch (error) {
        console.warn('⚠️ Error disconnecting worklet node:', error);
      }
      workletNodeRef.current = null;
    }

    // Stop all audio buffer sources
    console.log(`🔇 Stopping ${audioSourcesRef.current.size} audio sources...`);
    audioSourcesRef.current.forEach(source => {
      try {
        source.stop();
      } catch (error) {
        // Source might already be stopped
        console.warn('⚠️ Audio source already stopped:', error);
      }
    });
    audioSourcesRef.current.clear();

    // Close WebSocket session
    if (sessionRef.current) {
      console.log('🔌 Closing WebSocket session...');
      try {
        sessionRef.current.close();
      } catch (error) {
        console.warn('⚠️ Error closing WebSocket session:', error);
      }
      sessionRef.current = null;
    }

    // Close audio contexts
    if (inputAudioContextRef.current && inputAudioContextRef.current.state !== 'closed') {
      console.log('🔇 Closing input audio context...');
      try {
        inputAudioContextRef.current.close();
      } catch (error) {
        console.warn('⚠️ Error closing input audio context:', error);
      }
    }
    inputAudioContextRef.current = null;

    if (outputAudioContextRef.current && outputAudioContextRef.current.state !== 'closed') {
      console.log('🔇 Closing output audio context...');
      try {
        outputAudioContextRef.current.close();
      } catch (error) {
        console.warn('⚠️ Error closing output audio context:', error);
      }
    }
    outputAudioContextRef.current = null;

    // Clear gain node reference
    gainNodeRef.current = null;

    console.log('✅ Audio cleanup completed');
  }, []);

  // Effect to update company status when a new summary is set
  useEffect(() => {
      if (latestSummary && latestSummary.callSid) {
          console.log(`[${latestSummary.callSid}] New summary received, updating company status to completed.`);
          setCompanies(prev => prev.map(c =>
              c.callSid === latestSummary.callSid ? { ...c, status: 'completed' } : c
          ));
          // Clear active call SID now that processing is fully complete
          setCurrentCallSid(null);
          setCallStatus('completed');
      }
  }, [latestSummary]); // Run only when latestSummary changes

  const loadCampaignScript = async (id: number) => {
      setIsLoadingScript(true);
      toast.loading(`Loading Campaign ${id}...`, { id: 'load-script-toast' });
      try {
          // Use the backend API to load campaign scripts (handles both .json and .txt files)
          const response = await authenticatedFetch(`${BACKEND_URL}/get-campaign-script/${id}`);
          if (!response.ok) {
              let errorText = `Failed to fetch campaign ${id} (Status: ${response.status})`;
              try {
                  const errorData = await response.json();
                  errorText = errorData.error || errorText;
              } catch {
                  try {
                      const rawErrorText = await response.text();
                      errorText = rawErrorText.substring(0, 200) + (rawErrorText.length > 200 ? '...' : '');
                  } catch { /* Ignore text parsing errors */ }
              }
              throw new Error(errorText);
          }
          const data: CampaignData = await response.json();
          
          const fullCampaignJsonString = JSON.stringify(data, null, 2);
          setTask(fullCampaignJsonString);

          // Auto-detect and set language based on available campaigns (consistent with incoming calls)
          if (id === 1 || id === 2) {
              // Campaigns 1, 2 are English
              setSelectedLanguage('en');
              toast.success(`Campaign ${id} script loaded! Language set to English.`, { id: 'load-script-toast' });
          } else if (id === 3 || id === 4) {
              // Campaigns 3, 4 are Spanish
              setSelectedLanguage('es');
              toast.success(`Campaign ${id} script loaded! Language set to Spanish.`, { id: 'load-script-toast' });
          } else if (id === 5 || id === 6) {
              // Campaigns 5, 6 are Czech
              setSelectedLanguage('cz');
              toast.success(`Campaign ${id} script loaded! Language set to Czech.`, { id: 'load-script-toast' });
          } else {
              // Default to English for other campaigns
              setSelectedLanguage('en');
              toast.success(`Campaign ${id} script loaded! Language set to English.`, { id: 'load-script-toast' });
          }
      } catch (error) {
          console.error("Error loading campaign script:", error);
          toast.error(error instanceof Error ? error.message : 'Failed to load script', { id: 'load-script-toast' });
          setTask('');
      } finally {
          setIsLoadingScript(false);
      }
  };


  const addCompany = () => {
    if (companies.length < 12) {
      setCompanies([...companies, { name: '', phone: '' }]);
    }
  };

  const removeCompany = (index: number) => {
    const companyToRemove = companies[index];
    setCompanies(companies.filter((_, i) => i !== index));
    if (latestSummary && companyToRemove.callSid === latestSummary.callSid) {
        setLatestSummary(null);
    }
  };

  const updateCompanyName = (index: number, value: string) => {
    const newCompanies = [...companies];
    newCompanies[index] = { ...newCompanies[index], name: value };
    setCompanies(newCompanies);
  };

  const updateCompanyPhone = (index: number, value: string) => {
    const newCompanies = [...companies];
    newCompanies[index] = { ...newCompanies[index], phone: value };
    setCompanies(newCompanies);
  };

  // --- Polling Logic ---
  // Wrap pollForResults in useCallback to prevent it from changing on every render
  const pollForResults = useCallback(async (callSid: string, targetPhone: string, companyIndex: number) => {
    console.log(`[${callSid}] Polling for results...`);
    setCompanies(prev => prev.map((c, i) => (i === companyIndex && c.status !== 'polling-results') ? { ...c, status: 'polling-results' } : c));

    try {
      const response = await authenticatedFetch(`${BACKEND_URL}/call-results/${callSid}`);
      console.log(`[${callSid}] Poll response status: ${response.status}`);

      if (response.status === 200) { // Explicitly check for 200 OK
        const result = await response.json();
        console.log(`[${callSid}] Poll response data:`, result);

        // *** Enhanced: Include all available call data ***
        const displayResult: DisplayCallResult = {
            call_summary: result.info?.call_summary || 'No summary available.',
            customer_sentiment: result.info?.customer_sentiment || 'neutral',
            callSid: callSid,
            targetPhone: targetPhone,
            targetName: result.info?.targetName,
            targetPhoneNumber: result.info?.targetPhoneNumber,
            status: result.info?.status,
            timestamp: result.info?.timestamp,
            recordingUrl: result.info?.recordingUrl,
            recordingTimestamp: result.info?.recordingTimestamp,
            duration: result.info?.duration,
            callQuality: result.info?.callQuality
        };
        console.log(`[${callSid}] Setting latest summary:`, displayResult);

        setLatestSummary(displayResult); // Update the single latest summary
        // DO NOT update company status here - let the useEffect handle it
        toast.success(`Results received for call ${callSid}`);
        return true; // Stop polling
      }
      if (response.status === 404 || response.status === 202) {
        console.log(`[${callSid}] Results not ready (Status: ${response.status}). Continuing poll.`);
        return false; // Continue polling
      }

      const errorText = await response.text();
      console.error(`[${callSid}] Error fetching results:`, response.status, errorText);
      setLastError(`Error fetching results: ${response.status}`);
      setCompanies(prev => prev.map((c, i) => i === companyIndex ? { ...c, status: 'failed' } : c)); // Mark row as failed
      setCurrentCallSid(null); // Clear active call SID
      setCallStatus('failed');
      toast.error(`Error fetching results for call ${callSid}: ${response.status}`);
      return true; // Stop polling on error
    } catch (error) {
      console.error(`[${callSid}] Error polling for results:`, error);
      setLastError(`Polling error: ${error instanceof Error ? error.message : 'Unknown'}`);
      setCompanies(prev => prev.map((c, i) => i === companyIndex ? { ...c, status: 'failed' } : c)); // Mark row as failed
      setCurrentCallSid(null);
      setCallStatus('failed');
      toast.error(`Polling error for call ${callSid}`);
      return true; // Stop polling on catch
    }
  }, []);

  const handleSingleCall = async (company: Company, index: number) => {
    // Convert task content to JSON format for backend validation
    // The backend expects a JSON string, so we wrap the task content in a JSON object
    console.log(`🎯 [DEBUG] Making call with task content length: ${task.length}`);
    console.log(`🎯 [DEBUG] Task preview: ${task.substring(0, 200)}...`);

    const parsedTask = JSON.stringify({
      script: task,
      type: 'campaign_script'
    });

    console.log(`🎯 [DEBUG] Parsed task length: ${parsedTask.length}`);

    // Allow re-calling completed/failed rows

    if (isProcessing || !task || !fromPhoneNumber || !selectedVoice || !selectedModel) {
        toast.error('Please load a campaign script, select a model and voice, and ensure From Phone Number is set.');
        return;
    }
    if (!company.phone || !company.name) {
        toast.error(`Please fill in the Name and Phone Number for row ${index + 1}.`);
        return;
    }

    // Validate phone number format
    const phoneRegex = /^\+\d{10,15}$/;
    if (!phoneRegex.test(company.phone)) {
        toast.error(`Invalid phone number format for row ${index + 1}. Please use international format: +1234567890`);
        return;
    }

    setIsProcessing(true);
    setLastError(null);
    setLatestSummary(null); // Clear previous summary display
    setCurrentCallSid(null);
    setCallStatus('configuring');

    // Set status to in-progress for the specific row
    setCompanies(prev => prev.map((c, i) =>
      i === index ? { ...c, status: 'in-progress', callSid: undefined } : c
    ));


    try {
      // Update session config
      const configResponse = await authenticatedFetch(`${BACKEND_URL}/update-session-config`, {
        method: 'POST',
        body: JSON.stringify({
            task: parsedTask,
            voice: selectedVoice,
            model: selectedModel,
            targetName: company.name,
            targetPhoneNumber: company.phone,
            outputLanguage: selectedLanguage // Include selected language
        })
      });
      if (!configResponse.ok) {
        const errorData = await configResponse.json().catch(() => ({ error: 'Failed to parse error response' }));
        throw new Error(`Failed to update session config: ${errorData.error || configResponse.statusText}`);
      }

      // Make the call
      setCallStatus('initiating-call');
      // Note: Backend /make-call now uses the config set by /update-session-config above
      const callResponse = await authenticatedFetch(`${BACKEND_URL}/make-call`, {
        method: 'POST',
        body: JSON.stringify({
          to: company.phone, // Changed key to 'to'
          from: fromPhoneNumber, // Changed key to 'from'
          // No need to pass config here again, backend uses the one set above
        })
      });
      if (!callResponse.ok) {
        const errorData = await callResponse.json().catch(() => ({ error: 'Failed to parse error response' }));
        throw new Error(errorData.error || `Failed to initiate call to ${company.phone}`);
      }

      const callData = await callResponse.json();
      const sid = callData.callSid;
      if (!sid) {
        throw new Error('No call SID received from server');
      }

       // Update the specific company row with the new callSid and set overall status
       setCompanies(prev => prev.map((c, i) => i === index ? { ...c, callSid: sid, status: 'in-progress' } : c ));
       setCurrentCallSid(sid); // Set active SID to trigger polling effect
       setCallStatus('in-progress');

      toast.success(`Call initiated to ${company.phone}`);

      // Start polling (logic moved to useEffect triggered by currentCallSid)

    } catch (error) {
      const message = error instanceof Error ? error.message : 'An unknown error occurred during call setup';
      console.error(`Error processing call to ${company.phone}:`, error);
      setCompanies(prev => prev.map((c, i) => i === index ? { ...c, status: 'failed', callSid: undefined } : c ));
      setLastError(message);
      setCallStatus('failed');
      toast.error(message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleLocalTest = async (company: Company, index: number) => {
    console.log('🎤 Starting local audio test for:', company.name, company.phone);

    if (!task || !selectedVoice || !selectedModel) {
      console.error('❌ Missing required fields:', { task: !!task, voice: selectedVoice, model: selectedModel });
      toast.error('Please load a campaign script, select a model and voice.');
      return;
    }
    if (!company.phone || !company.name) {
      console.error('❌ Missing company details:', { name: company.name, phone: company.phone });
      toast.error(`Please fill in the Name and Phone Number for row ${index + 1}.`);
      return;
    }

    // Start local audio testing
    setCompanies(prev => prev.map((c, i) =>
      i === index ? { ...c, status: 'in-progress' } : c
    ));

    try {
      console.log('🎙️ Initializing audio contexts...');
      setAudioStatus('Initializing audio...');

      // Initialize audio contexts on user gesture
      initializeAudioContexts();

      console.log('🎙️ Requesting microphone access...');
      setAudioStatus('Requesting microphone access...');

      // Get microphone access
      mediaStreamRef.current = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      });
      console.log('✅ Microphone access granted');

      if (!inputAudioContextRef.current) {
        console.error('❌ Input audio context not initialized');
        throw new Error('Input audio context not initialized');
      }

      console.log('🔊 Audio context states:', {
        input: inputAudioContextRef.current.state,
        output: outputAudioContextRef.current?.state
      });

      // Resume audio context if suspended
      if (inputAudioContextRef.current.state === 'suspended') {
        console.log('▶️ Resuming input audio context...');
        await inputAudioContextRef.current.resume();
        console.log('✅ Input audio context resumed');
      }

      if (outputAudioContextRef.current && outputAudioContextRef.current.state === 'suspended') {
        console.log('▶️ Resuming output audio context...');
        await outputAudioContextRef.current.resume();
        console.log('✅ Output audio context resumed');
      }

      // Initialize Gemini session for incoming testing
      console.log('🚀 Initializing Gemini session for incoming testing...');
      await initializeGeminiSession(company, 'incoming');

      // Create audio processing chain using modern AudioWorkletNode
      sourceNodeRef.current = inputAudioContextRef.current.createMediaStreamSource(mediaStreamRef.current);
      console.log('📡 Audio source created');

      // Load AudioWorklet processor
      try {
        await inputAudioContextRef.current.audioWorklet.addModule('/api/audio-processor');
        console.log('✅ AudioWorklet module loaded successfully');

        // Create AudioWorkletNode
        workletNodeRef.current = new AudioWorkletNode(inputAudioContextRef.current, 'audio-processor');
        console.log('✅ AudioWorkletNode created successfully');

        let audioPacketCount = 0;

        // Handle audio data from AudioWorklet
        workletNodeRef.current.port.onmessage = (event) => {
          if (!sessionRef.current) return;

          const { type, data } = event.data;
          if (type === 'audioData') {
            // Calculate audio level for debugging
            const pcmData = new Int16Array(data.buffer);
            let sum = 0;
            let maxLevel = 0;
            for (let i = 0; i < pcmData.length; i++) {
              const level = Math.abs(pcmData[i] / 32768);
              sum += level;
              maxLevel = Math.max(maxLevel, level);
            }
            const avgLevel = sum / pcmData.length;

            // Log audio levels every 50 packets for debugging
            audioPacketCount++;
            if (audioPacketCount % 50 === 0) {
              console.log(`🎤 Audio levels - Avg: ${avgLevel.toFixed(4)}, Max: ${maxLevel.toFixed(4)}`);
            }

            // Lower threshold for audio detection
            if (maxLevel < 0.001) return; // Skip very quiet audio

            try {
              const base64Audio = btoa(String.fromCharCode(...data));

              sessionRef.current.sendRealtimeInput({
                media: {
                  data: base64Audio,
                  mimeType: 'audio/pcm;rate=16000'
                }
              });

              if (audioPacketCount % 100 === 0) {
                console.log(`📤 Sent audio packet #${audioPacketCount}, size: ${data.length} bytes`);
              }
            } catch (error) {
              console.error('❌ Error sending audio data:', error);
            }
          }
        };

        // Connect the audio processing chain
        sourceNodeRef.current.connect(workletNodeRef.current);
        workletNodeRef.current.connect(inputAudioContextRef.current.destination);

      } catch (error) {
        console.error('❌ Error setting up AudioWorklet, falling back to ScriptProcessorNode:', error);

        // Fallback to ScriptProcessorNode if AudioWorklet fails
        const scriptProcessor = inputAudioContextRef.current.createScriptProcessor(4096, 1, 1);
        console.log('📡 Fallback: ScriptProcessor created with buffer size 4096');

        let audioPacketCount = 0;
        scriptProcessor.onaudioprocess = (audioProcessingEvent) => {
          if (!sessionRef.current) return;

          const inputBuffer = audioProcessingEvent.inputBuffer;
          const pcmData = inputBuffer.getChannelData(0);

          // Calculate audio level for debugging
          let sum = 0;
          let maxLevel = 0;
          for (let i = 0; i < pcmData.length; i++) {
            const level = Math.abs(pcmData[i]);
            sum += level;
            maxLevel = Math.max(maxLevel, level);
          }
          const avgLevel = sum / pcmData.length;

          // Log audio levels every 50 packets for debugging
          audioPacketCount++;
          if (audioPacketCount % 50 === 0) {
            console.log(`🎤 Audio levels - Avg: ${avgLevel.toFixed(4)}, Max: ${maxLevel.toFixed(4)}`);
          }

          // Lower threshold for audio detection
          if (maxLevel < 0.001) return; // Skip very quiet audio

          try {
            const int16Array = new Int16Array(pcmData.length);
            for (let i = 0; i < pcmData.length; i++) {
              int16Array[i] = Math.max(-32768, Math.min(32767, pcmData[i] * 32768));
            }

            const uint8Array = new Uint8Array(int16Array.buffer);
            const base64Audio = btoa(String.fromCharCode(...uint8Array));

            sessionRef.current.sendRealtimeInput({
              media: {
                data: base64Audio,
                mimeType: 'audio/pcm;rate=16000'
              }
            });

            if (audioPacketCount % 100 === 0) {
              console.log(`📤 Sent audio packet #${audioPacketCount}, size: ${uint8Array.length} bytes`);
            }
          } catch (error) {
            console.error('❌ Error sending audio data:', error);
          }
        };

        // Connect the audio processing chain
        sourceNodeRef.current.connect(scriptProcessor);
        scriptProcessor.connect(inputAudioContextRef.current.destination);

        // Store reference for cleanup
        workletNodeRef.current = scriptProcessor as unknown as AudioWorkletNode;
      }
      console.log('🔗 Audio processing chain connected');

      setIsRecording(true);
      setAudioStatus('Recording... Speak now!');
      toast.success(`Local audio test started for ${company.name} (${company.phone})`);

    } catch (error) {
      const message = error instanceof Error ? error.message : 'An unknown error occurred during local test';
      console.error(`❌ CRITICAL ERROR in local test for ${company.phone}:`, error);
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      setCompanies(prev => prev.map((c, i) => i === index ? { ...c, status: 'failed' } : c ));
      setAudioStatus('Failed to start recording');
      toast.error(message);

      // Cleanup on error
      cleanupAudio();
    }
  };

  const initializeGeminiSession = async (company: Company, testType: 'incoming' | 'outbound' = 'outbound') => {
    try {
      setAudioStatus('Connecting to Gemini Live API...');

      // Create WebSocket connection to backend for Gemini session
      // For local testing, try localhost first, then fallback to production URL
      const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

      // Use different endpoints based on test type
      const endpoint = testType === 'incoming' ? '/test-inbound' : '/test-outbound';
      const wsUrl = isLocalhost
        ? `ws://localhost:3101${endpoint}`
        : `${BACKEND_URL.replace('http', 'ws')}${endpoint}`;

      console.log('Connecting to WebSocket:', wsUrl);
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('WebSocket connected successfully');
        setIsConnected(true);
        setAudioStatus('Connected, starting session...');

        // Send start-session message to initiate the session
        let campaignScript = null;
        let aiInstructions = '';

        // Try to parse task as campaign script JSON and convert to AI instructions
        try {
          const parsedTask = JSON.parse(task);
          if (parsedTask.success && parsedTask.script) {
            campaignScript = parsedTask.script;
            aiInstructions = convertCampaignToInstructions(parsedTask.script);
          } else if (parsedTask.agentPersona || parsedTask.campaign) {
            campaignScript = parsedTask;
            aiInstructions = convertCampaignToInstructions(parsedTask);
          } else {
            // Plain text instructions
            aiInstructions = task;
          }
        } catch {
          // Not JSON, treat as plain text instructions
          aiInstructions = task;
        }

        // Function to convert campaign script to AI instructions
        function convertCampaignToInstructions(campaign: Record<string, unknown>): string {
          try {
            // Simple approach: convert the entire campaign to a readable instruction string
            const campaignStr = JSON.stringify(campaign, null, 2);
            return `You are an AI assistant. Follow this campaign script exactly: ${campaignStr}`;
          } catch (error) {
            console.error('Error converting campaign to instructions:', error);
            return 'You are a helpful customer service representative.';
          }
        }

        const startMessage = {
          type: 'start-session',
          voice: selectedVoice,
          model: selectedModel,
          language: selectedLanguage,
          aiInstructions: aiInstructions,
          campaignScript: campaignScript
        };
        console.log('📤 Sending start-session message:', startMessage);
        ws.send(JSON.stringify(startMessage));
      };

      ws.onmessage = async (event) => {
        console.log('📨 Received WebSocket message:', event.data);
        const data = JSON.parse(event.data);

        // Handle session started confirmation
        if (data.type === 'session-started') {
          console.log('✅ Session established successfully');
          setAudioStatus('Session ready, audio processing active...');

          // Start 10-second countdown timer for AI initialization
          setAiInitTimer(10);
          setAudioStatus('AI will start speaking in 10 seconds...');
          const timerInterval = setInterval(() => {
            setAiInitTimer(prev => {
              if (prev <= 1) {
                clearInterval(timerInterval);
                setAudioStatus('AI should start speaking now...');
                return 0;
              }
              setAudioStatus(`AI will start speaking in ${prev - 1} seconds...`);
              return prev - 1;
            });
          }, 1000);
          return;
        }
        console.log('📋 Parsed message data:', data);

        if (data.type === 'audio' && outputAudioContextRef.current && gainNodeRef.current) {
          console.log('🔊 Received audio from Gemini, size:', data.audio?.length || 0, 'mimeType:', data.mimeType);
          // Handle incoming audio from Gemini
          try {
            const audioData = new Uint8Array(atob(data.audio).split('').map(c => c.charCodeAt(0)));
            console.log('🎵 Decoded audio data, length:', audioData.length);

            // Extract sample rate from mime type if available (e.g., "audio/pcm;rate=24000")
            let sampleRate = 24000; // Default
            if (data.mimeType && data.mimeType.includes('rate=')) {
              const match = data.mimeType.match(/rate=(\d+)/);
              if (match) {
                sampleRate = parseInt(match[1]);
                console.log('🎵 Using sample rate from mimeType:', sampleRate);
              }
            }

            const audioBuffer = await decodeAudioData(audioData, outputAudioContextRef.current, sampleRate, 1);
            console.log('🎶 Created audio buffer, duration:', audioBuffer.duration);

            const source = outputAudioContextRef.current.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(gainNodeRef.current!);

            source.addEventListener('ended', () => {
              console.log('🔇 Audio playback ended');
              audioSourcesRef.current.delete(source);
            });

            const startTime = Math.max(nextStartTimeRef.current, outputAudioContextRef.current.currentTime);
            console.log('▶️ Starting audio playback at time:', startTime, 'context state:', outputAudioContextRef.current.state);
            
            // Ensure audio context is running
            if (outputAudioContextRef.current.state === 'suspended') {
              console.log('🔊 Resuming suspended audio context...');
              await outputAudioContextRef.current.resume();
            }
            
            source.start(startTime);
            nextStartTimeRef.current = startTime + audioBuffer.duration;
            audioSourcesRef.current.add(source);
            console.log('✅ Audio playback scheduled successfully');

          } catch (error) {
            console.error('❌ Error playing audio:', error);
          }
        } else if (data.type === 'error') {
          console.error('❌ Received error from backend:', data.message);
        } else {
          console.log('ℹ️ Received other message type:', data.type);
        }
      };

      ws.onerror = (error) => {
        console.error('❌ WebSocket error occurred:', error);
        setAudioStatus('Connection error');
        setIsConnected(false);
      };

      ws.onclose = (event) => {
        console.log('🔌 WebSocket connection closed:', {
          code: event.code,
          reason: event.reason,
          wasClean: event.wasClean
        });
        setIsConnected(false);
        setAudioStatus('Disconnected');
      };

      // Create session wrapper
      sessionRef.current = {
        sendRealtimeInput: (data: { media: { data: string; mimeType: string } }) => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'audio-data',
              audioData: data.media.data
            }));
          }
        },
        close: () => {
          ws.close();
        }
      };

    } catch (error) {
      console.error('Error initializing Gemini session:', error);
      setAudioStatus('Failed to connect');
      toast.error('Failed to connect to Gemini Live API');
    }
  };

  // Utility function to decode audio data
  const decodeAudioData = async (
    data: Uint8Array,
    ctx: AudioContext,
    sampleRate: number,
    numChannels: number
  ): Promise<AudioBuffer> => {
    const buffer = ctx.createBuffer(numChannels, data.length / 2 / numChannels, sampleRate);
    const dataInt16 = new Int16Array(data.buffer);
    const dataFloat32 = new Float32Array(dataInt16.length);

    for (let i = 0; i < dataInt16.length; i++) {
      dataFloat32[i] = dataInt16[i] / 32768.0;
    }

    if (numChannels === 1) {
      buffer.copyToChannel(dataFloat32, 0);
    } else {
      for (let i = 0; i < numChannels; i++) {
        const channel = dataFloat32.filter((_, index) => index % numChannels === i);
        buffer.copyToChannel(channel, i);
      }
    }

    return buffer;
  };

  // Comprehensive state reset function
  const resetApplicationState = useCallback(() => {
    console.log('🔄 Resetting application state...');

    // Reset all session tracking
    setCurrentCallSid(null);
    setCallStatus('idle');
    setIsProcessing(false);
    setLatestSummary(null);
    setLastError(null);
    setAiInitTimer(0);

    // Reset audio state
    setIsRecording(false);
    setIsConnected(false);
    setAudioStatus('Ready to start local testing');

    // Reset company statuses to remove any in-progress states
    setCompanies(prev => prev.map(company => {
      const shouldReset = company.status === 'in-progress' ||
                         company.status === 'polling-results' ||
                         company.status === 'pending';
      return {
        ...company,
        status: shouldReset ? undefined : company.status,
        callSid: shouldReset ? undefined : company.callSid
      };
    }));

    console.log('✅ Application state reset completed');
  }, []);

  // Enhanced session termination function with visual feedback
  const terminateActiveSession = useCallback(async (reason: string = 'Mode switch') => {
    console.log(`🛑 Terminating active session - Reason: ${reason}`);
    setIsTerminating(true);

    try {
      // Stop all audio processing and WebSocket connections
      cleanupAudio();

      // Clear all timers and intervals
      if (pollIntervalRef.current) {
        console.log('⏰ Clearing polling interval...');
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }

      // Reset application state
      resetApplicationState();

      // Small delay to show termination feedback
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('✅ Session termination completed');
    } finally {
      setIsTerminating(false);
    }
  }, [cleanupAudio, resetApplicationState]);

  const stopLocalTest = useCallback((index: number) => {
    console.log(`🛑 Stopping local test for company index ${index}`);
    setAudioStatus('Stopping...');

    // Use the enhanced termination logic
    terminateActiveSession('User stopped local test');

    // Mark the specific company as completed
    setCompanies(prev => prev.map((c, i) =>
      i === index ? { ...c, status: 'completed' } : c
    ));

    setAudioStatus('Stopped');
    toast.success('Local audio testing stopped');
  }, [terminateActiveSession]);

  // Enhanced mode switching with detailed feedback
  const performModeSwitch = useCallback(async (newActiveTab?: 'outbound' | 'incoming', newAudioMode?: 'twilio' | 'local', fromActiveTab?: 'outbound' | 'incoming', fromAudioMode?: 'twilio' | 'local') => {
    const wasActiveSession = isRecording || isConnected || currentCallSid || callStatus !== 'idle';

    if (wasActiveSession) {
      console.log('🔄 Mode switch detected with active session - performing cleanup...');

      // Show immediate feedback about the switch with correct from/to modes
      let fromMode, toMode;
      if (newActiveTab && fromActiveTab) {
        fromMode = fromActiveTab;
        toMode = newActiveTab;
      } else if (newAudioMode && fromAudioMode) {
        fromMode = fromAudioMode === 'twilio' ? 'Twilio' : 'local testing';
        toMode = newAudioMode === 'twilio' ? 'Twilio' : 'local testing';
      } else {
        // Fallback for unclear switches
        fromMode = 'current mode';
        toMode = newActiveTab || newAudioMode || 'new mode';
      }

      toast.loading(`Switching from ${fromMode} to ${toMode}...`, {
        duration: 2000,
        icon: '🔄'
      });

      // Terminate the active session using the current reference
      await terminateActiveSession('Mode switch');

      // Show success feedback
      const modeDescription = newActiveTab ? `${newActiveTab} calls` : `${newAudioMode} mode`;
      toast.success(`Successfully switched to ${modeDescription}`, {
        duration: 3000,
        icon: '✅'
      });
    } else {
      // No active session, just show simple switch confirmation
      const modeDescription = newActiveTab ? `${newActiveTab} calls` : `${newAudioMode} mode`;
      toast.success(`Switched to ${modeDescription}`, {
        duration: 2000,
        icon: '🔄'
      });
    }
  }, [isRecording, isConnected, currentCallSid, callStatus]);

  // Track previous values for proper mode switching
  const prevActiveTab = useRef<'outbound' | 'incoming'>('outbound');
  const prevAudioMode = useRef<'twilio' | 'local'>('twilio');

  // Mode switching effect for activeTab (outbound/incoming)
  useEffect(() => {
    // Skip on initial mount
    if (isInitialMount.current) {
      prevActiveTab.current = activeTab;
      return;
    }

    // Only trigger mode switch if the value actually changed
    if (prevActiveTab.current !== activeTab) {
      performModeSwitch(activeTab, undefined, prevActiveTab.current, undefined);
      prevActiveTab.current = activeTab;
    }
  }, [activeTab, performModeSwitch]);

  // Mode switching effect for audioMode (twilio/local)
  useEffect(() => {
    // Skip on initial mount
    if (isInitialMount.current) {
      prevAudioMode.current = audioMode;
      return;
    }

    // Only trigger mode switch if the value actually changed
    if (prevAudioMode.current !== audioMode) {
      performModeSwitch(undefined, audioMode, undefined, prevAudioMode.current);
      prevAudioMode.current = audioMode;
    }
  }, [audioMode, performModeSwitch]);

  // Effect to start/stop polling based on currentCallSid
  useEffect(() => {
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
      pollIntervalRef.current = null;
    }

    if (currentCallSid && callStatus === 'in-progress') {
      console.log(`[${currentCallSid}] Starting polling interval.`);
      // Find the index and phone number associated with the currentCallSid
      const currentCompanyIndex = companies.findIndex(c => c.callSid === currentCallSid);
      const currentTargetPhone = companies[currentCompanyIndex]?.phone || 'unknown';

      if (currentCompanyIndex === -1) {
          console.warn(`[${currentCallSid}] Could not find company index for active call. Call may have completed.`);
          setCurrentCallSid(null); // Stop polling if index not found
          setCallStatus('idle');
          return;
      }

      pollIntervalRef.current = setInterval(async () => {
        const stopPolling = await pollForResults(currentCallSid, currentTargetPhone, currentCompanyIndex); // Pass index
        if (stopPolling && pollIntervalRef.current) {
          console.log(`[${currentCallSid}] Stopping polling interval.`);
          clearInterval(pollIntervalRef.current);
          pollIntervalRef.current = null;
        }
      }, 5000);
    }

    return () => {
      if (pollIntervalRef.current) {
        console.log(`[${currentCallSid || 'N/A'}] Clearing polling interval on cleanup.`);
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
    };
  }, [currentCallSid, callStatus, pollForResults, companies]);


  // Determine if call button should be disabled
  const isCallButtonDisabled = (company: Company) => {
      return isProcessing || // Global lock
             company.status === 'in-progress' || // This specific row is calling
             company.status === 'polling-results' || // This specific row is polling
             !company.name || !company.phone || !task || !fromPhoneNumber || !selectedModel; // Required fields missing
  };

  return (
    <div className={`min-h-screen transition-colors duration-200 ${theme === 'dark' ? 'dark' : 'light'}`} style={{
      backgroundColor: theme === 'dark' ? 'hsl(155, 20%, 8%)' : 'hsl(155, 10%, 99%)',
      color: theme === 'dark' ? 'hsl(150, 10%, 98%)' : 'hsl(155, 40%, 20%)',
      overflowY: 'scroll' // Force scrollbar to always be present to prevent layout shift
    }}>
      <ThemeToggle />
      <main className="min-h-screen p-8 bg-background transition-colors duration-200">
        <div className="max-w-4xl mx-auto card p-6 md:p-8" style={{
          backgroundColor: theme === 'dark' ? 'hsl(155, 25%, 12%)' : 'hsl(0, 0%, 100%)',
          border: theme === 'dark' ? '1px solid hsl(155, 25%, 20%)' : '1px solid hsl(155, 15%, 85%)',
          borderRadius: '0.5rem',
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)'
        }}>
          <h1 className="text-3xl font-bold mb-8 text-center text-foreground" style={{
            color: theme === 'dark' ? 'hsl(150, 10%, 98%)' : 'hsl(155, 40%, 20%)'
          }}>Gemini Live Voice AI</h1>

          {/* Session Termination Indicator */}
          {isTerminating && (
            <div className="flex items-center justify-center gap-3 p-4 mb-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-yellow-600"></div>
              <span className="text-yellow-800 dark:text-yellow-200 font-medium">
                Terminating active session...
              </span>
            </div>
          )}

          {/* Tab Navigation */}
          <div className="flex justify-center mb-6">
            <div className="bg-muted p-1 rounded-lg inline-flex">
              <button
                onClick={() => {
                  if (activeTab !== 'outbound' && !isTerminating) {
                    setActiveTab('outbound');
                  }
                }}
                disabled={isTerminating}
                className={cn(
                  "px-6 py-2 rounded-md text-sm font-medium transition-colors",
                  activeTab === 'outbound'
                    ? "bg-primary text-primary-foreground shadow-sm"
                    : "text-muted-foreground hover:text-foreground",
                  isTerminating && "opacity-50 cursor-not-allowed"
                )}
              >
                {isTerminating && activeTab !== 'outbound' ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-3 w-3 border-b border-current"></div>
                    Outbound Calls
                  </div>
                ) : (
                  'Outbound Calls'
                )}
              </button>
              <button
                onClick={() => {
                  if (activeTab !== 'incoming' && !isTerminating) {
                    setActiveTab('incoming');
                  }
                }}
                disabled={isTerminating}
                className={cn(
                  "px-6 py-2 rounded-md text-sm font-medium transition-colors",
                  activeTab === 'incoming'
                    ? "bg-primary text-primary-foreground shadow-sm"
                    : "text-muted-foreground hover:text-foreground",
                  isTerminating && "opacity-50 cursor-not-allowed"
                )}
              >
                {isTerminating && activeTab !== 'incoming' ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-3 w-3 border-b border-current"></div>
                    Incoming Calls
                  </div>
                ) : (
                  'Incoming Calls'
                )}
              </button>
            </div>
          </div>

          {/* Incoming Calls Toggle - Only show when incoming tab is active */}
          {activeTab === 'incoming' && (
            <div className="flex flex-col items-center gap-3 mb-6">
              <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                <label htmlFor="incoming-toggle" className="text-sm font-medium text-foreground">
                  Enable Incoming Calls Processing
                </label>
                <button
                  id="incoming-toggle"
                  onClick={() => {
                    const newState = !incomingCallsEnabled;
                    setIncomingCallsEnabled(newState);
                    // If enabling incoming calls, show info about default campaign
                    if (newState) {
                      toast('Incoming calls enabled - will use selected campaign script or default to Campaign 1', {
                        duration: 4000,
                        icon: '✅'
                      });
                    }
                  }}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    incomingCallsEnabled
                      ? "bg-primary"
                      : "bg-muted-foreground/20"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      incomingCallsEnabled ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
                <span className={cn(
                  "text-sm font-medium",
                  incomingCallsEnabled ? "text-green-600 dark:text-green-400" : "text-muted-foreground"
                )}>
                  {incomingCallsEnabled ? "ON" : "OFF"}
                </span>
              </div>


            </div>
          )}

          {/* Audio Mode Toggle */}
          <div className="flex justify-center mb-6">
            <div className="bg-muted p-1 rounded-lg inline-flex">
              <button
                onClick={() => {
                  if (audioMode !== 'twilio' && !isTerminating) {
                    setAudioMode('twilio');
                  }
                }}
                disabled={isTerminating}
                className={cn(
                  "px-4 py-2 rounded-md text-sm font-medium transition-colors",
                  audioMode === 'twilio'
                    ? "bg-primary text-primary-foreground shadow-sm"
                    : "text-muted-foreground hover:text-foreground",
                  isTerminating && "opacity-50 cursor-not-allowed"
                )}
              >
                {isTerminating && audioMode !== 'twilio' ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-3 w-3 border-b border-current"></div>
                    Twilio
                  </div>
                ) : (
                  'Twilio'
                )}
              </button>
              <button
                onClick={() => {
                  if (audioMode !== 'local' && !isTerminating) {
                    setAudioMode('local');
                  }
                }}
                disabled={isTerminating}
                className={cn(
                  "px-4 py-2 rounded-md text-sm font-medium transition-colors",
                  audioMode === 'local'
                    ? "bg-green-600 text-white shadow-sm"
                    : "text-muted-foreground hover:text-foreground",
                  isTerminating && "opacity-50 cursor-not-allowed"
                )}
              >
                {isTerminating && audioMode !== 'local' ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-3 w-3 border-b border-current"></div>
                    Local Testing
                  </div>
                ) : (
                  'Local Testing'
                )}
              </button>
            </div>
          </div>

          {/* De-essing Audio Filter Toggle */}
          <div className="flex justify-center mb-8">
            <DeEssingToggle />
          </div>

          {/* Conditional Content */}
          {activeTab === 'outbound' ? (
            <form onSubmit={(e) => e.preventDefault()} className="space-y-6">
          {/* Country Selection Dropdown */}
          <div>
            <label htmlFor="countrySelect" className="block text-sm font-medium mb-2 text-foreground">
              Select Country / Twilio Number
            </label>
            <select
              id="countrySelect"
              value={selectedCountry}
              onChange={(e) => setSelectedCountry(e.target.value)}
              className={cn(
                "w-full p-2 border border-border rounded-md shadow-sm",
                "focus:ring-primary focus:border-primary bg-input text-foreground",
                "transition-colors duration-200"
              )}
            >
              {AVAILABLE_COUNTRIES.map((country) => (
                <option key={country.code} value={country.code}>
                  {country.flag} {country.name} ({country.phoneNumber})
                </option>
              ))}
            </select>
          </div>



          {/* Model Selection Dropdown */}
          <div>
            <label htmlFor="modelSelect" className="block text-sm font-medium mb-2 text-foreground">
              Select AI Model
            </label>
            <select
              id="modelSelect"
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              className={cn(
                "w-full p-2 border border-border rounded-md shadow-sm",
                "focus:ring-primary focus:border-primary bg-input text-foreground",
                "transition-colors duration-200"
              )}
            >
              {Object.entries(availableModels).map(([modelId, modelInfo]) => (
                <option key={modelId} value={modelId}>
                  {typeof modelInfo === 'object' && modelInfo !== null && 'name' in modelInfo ? (modelInfo as { name: string }).name : String(modelInfo)}
                </option>
              ))}
            </select>
          </div>

          {/* Voice Selection Dropdown */}
          <div>
            <label htmlFor="voiceSelect" className="block text-sm font-medium mb-2 text-foreground">
              Select AI Voice Sound
            </label>
            <select
              id="voiceSelect"
              value={selectedVoice}
              onChange={(e) => setSelectedVoice(e.target.value)}
              className={cn(
                "w-full p-2 border border-border rounded-md shadow-sm",
                "focus:ring-primary focus:border-primary bg-input text-foreground",
                "transition-colors duration-200"
              )}
            >
              {availableVoices.map((voice) => {
                const details = voiceDetails[voice] as { 
                  gender?: string; 
                  characteristics?: string;
                  description?: string;
                  useCase?: string;
                } | undefined;
                
                // Use voice descriptions from .env if available, otherwise fall back to voiceDetails
                const envDescription = voiceDescriptions[voice];
                const displayText = envDescription || (details
                  ? `${voice} - ${details.gender || 'Unknown'} (${details.characteristics || 'No description'})`
                  : voice.charAt(0).toUpperCase() + voice.slice(1));
                
                return (
                  <option 
                    key={voice} 
                    value={voice} 
                    className="bg-input text-foreground"
                  >
                    {displayText}
                  </option>
                );
              })}
            </select>
            
            {selectedVoice && voiceDetails[selectedVoice] ? (() => {
              const details = voiceDetails[selectedVoice];
              return (
                <div className="mt-2 p-3 bg-muted rounded-md">
                  <div className="text-sm space-y-2">
                    <div>
                      <span className="font-medium text-foreground">{selectedVoice}</span>
                      {details.gender && (
                        <span className="ml-2 px-2 py-0.5 bg-primary/10 text-primary text-xs rounded">
                          {details.gender}
                        </span>
                      )}
                    </div>
                    {details.characteristics && (
                      <div className="text-muted-foreground">
                        <strong>Voice characteristics:</strong> {details.characteristics}
                      </div>
                    )}
                    {details.pitch && (
                      <div className="text-muted-foreground">
                        <strong>Pitch:</strong> {details.pitch}
                      </div>
                    )}
                    {details.timbre && (
                      <div className="text-muted-foreground">
                        <strong>Timbre:</strong> {details.timbre}
                      </div>
                    )}
                    {details.persona && (
                      <div className="text-muted-foreground">
                        <strong>Persona:</strong> {details.persona}
                      </div>
                    )}
                  </div>
                </div>
              );
            })() : null}
          </div>

          {/* Language Selection Dropdown */}
          <div>
            <label htmlFor="languageSelect" className="block text-sm font-medium mb-2 text-foreground">
              Select Output Language
            </label>
            <select
              id="languageSelect"
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value)}
              className={cn(
                "w-full p-2 border border-border rounded-md shadow-sm",
                "focus:ring-primary focus:border-primary bg-input text-foreground",
                "transition-colors duration-200"
              )}
            >
              {AVAILABLE_LANGUAGES.map((lang) => (
                <option key={lang.code} value={lang.code}>
                  {lang.name}
                </option>
              ))}
            </select>
          </div>


          {/* Campaign Load Buttons - Available Campaigns Only */}
          <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                Load Outbound Campaign Script (Available Campaigns)
              </label>

              {/* Header Row - Category Labels */}
              <div className="grid grid-cols-6 gap-2 text-xs font-semibold text-muted-foreground text-center">
                  <div className="p-2 bg-accent/20 rounded">English Insurance</div>
                  <div className="p-2 bg-accent/20 rounded">English Fundraising</div>
                  <div className="p-2 bg-accent/20 rounded">Spanish Insurance</div>
                  <div className="p-2 bg-accent/20 rounded">Spanish Fundraising</div>
                  <div className="p-2 bg-accent/20 rounded">Czech Insurance</div>
                  <div className="p-2 bg-accent/20 rounded">Czech Fundraising</div>
              </div>

              {/* Campaign Buttons Grid - 6 Buttons (1-6) */}
              <div className="grid grid-cols-6 gap-2">
                  <button
                      type="button"
                      onClick={() => loadCampaignScript(1)}
                      disabled={isLoadingScript}
                      className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                      <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                      1 (Out)
                  </button>
                  <button
                      type="button"
                      onClick={() => loadCampaignScript(2)}
                      disabled={isLoadingScript}
                      className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                      <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                      2 (Out)
                  </button>
                  <button
                      type="button"
                      onClick={() => loadCampaignScript(3)}
                      disabled={isLoadingScript}
                      className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                      <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                      3 (Out)
                  </button>
                  <button
                      type="button"
                      onClick={() => loadCampaignScript(4)}
                      disabled={isLoadingScript}
                      className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                      <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                      4 (Out)
                  </button>
                  <button
                      type="button"
                      onClick={() => loadCampaignScript(5)}
                      disabled={isLoadingScript}
                      className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                      <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                      5 (Out)
                  </button>
                  <button
                      type="button"
                      onClick={() => loadCampaignScript(6)}
                      disabled={isLoadingScript}
                      className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                      <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                      6 (Out)
                  </button>
              </div>
          </div>


          {/* Task Description Input - Now preloaded with script */}
          <div>
            <label htmlFor="taskDescription" className="block text-sm font-medium mb-2 text-foreground">
              Campaign Script / Task Description (Text Format - JSON supported)
            </label>
            <textarea
              id="taskDescription"
              value={task}
              onChange={(e) => setTask(e.target.value)}
              className="w-full p-2 border border-border rounded-md shadow-sm focus:ring-primary focus:border-primary font-mono text-xs bg-input text-foreground"
              rows={15}
              placeholder="Load a campaign script using the buttons above or paste text/JSON here..."
              required
            />
          </div>

          {/* Call Interface - Same UI for both Local and Twilio */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold mb-2 text-foreground">
              {audioMode === 'local' ? 'Local Audio Testing (Browser)' : 'Phone Numbers to Call'}
            </h3>
              {companies.map((company, index) => (
                <div key={index} className="flex flex-col sm:flex-row gap-4 items-start border border-border rounded-lg p-4 bg-card">
                  {/* Name Input */}
                  <div className="flex-1 w-full sm:w-1/3">
                     <label htmlFor={`companyName-${index}`} className="sr-only">Target Name</label>
                     <input
                      id={`companyName-${index}`}
                      type="text"
                      value={company.name}
                      onChange={(e) => updateCompanyName(index, e.target.value)}
                      placeholder="Target Name"
                      className="w-full p-2 border border-border rounded-md shadow-sm focus:ring-primary focus:border-primary bg-input text-foreground"
                      required
                    />
                  </div>
                   {/* Phone Input */}
                  <div className="flex-1 w-full sm:w-1/3">
                     <label htmlFor={`companyPhone-${index}`} className="sr-only">Phone Number (To Call)</label>
                    <input
                      id={`companyPhone-${index}`}
                      type="tel"
                      value={company.phone}
                      onChange={(e) => updateCompanyPhone(index, e.target.value)}
                      placeholder="Phone number (To Call) - Use international format: +1234567890"
                      className="w-full p-2 border border-border rounded-md shadow-sm focus:ring-primary focus:border-primary bg-input text-foreground"
                      required
                    />
                  </div>
                  {/* Call Button */}
                  <div className="flex-1 w-full sm:w-1/3 flex justify-center sm:justify-end items-center gap-2">
                    {audioMode === 'local' && isRecording && company.status === 'in-progress' ? (
                      <button
                        type="button"
                        onClick={() => stopLocalTest(index)}
                        className="bg-red-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Stop
                      </button>
                    ) : (
                      <button
                        type="button"
                        onClick={() => audioMode === 'local' ? handleLocalTest(company, index) : handleSingleCall(company, index)}
                        disabled={isCallButtonDisabled(company)}
                        className="bg-green-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {audioMode === 'local' ? (
                          <>Test</>
                        ) : (
                          <>Call</>
                        )}
                      </button>
                    )}
                    {/* Remove Button */}
                    <button
                      type="button"
                      onClick={() => removeCompany(index)}
                      className="bg-red-100 text-red-600 p-2 rounded-md shadow-sm hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      aria-label="Remove company"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                  {/* Status Display */}
                  <div className="w-full sm:w-auto text-sm text-center sm:text-left mt-2 sm:mt-0 text-gray-600">
                      {company.status && (
                          <div>
                              Status: <span className={`font-medium px-1.5 py-0.5 rounded ${
                                  company.status === 'completed' ? 'bg-green-100 text-green-800' :
                                  company.status === 'failed' ? 'bg-red-100 text-red-800' :
                                  company.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                                  company.status === 'polling-results' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-gray-100 text-gray-800'
                              }`}>
                                  {company.status.replace('-', ' ')}
                              </span>
                          </div>
                      )}
                  </div>
                </div>
              ))}
              {/* Add Company Button */}
              <button
                type="button"
                onClick={addCompany}
                disabled={companies.length >= 12}
                className="mt-2 inline-flex items-center px-3 py-2 border border-border shadow-sm text-sm leading-4 font-medium rounded-md text-foreground bg-card hover:bg-accent focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <PlusIcon className="-ml-0.5 mr-2 h-4 w-4" aria-hidden="true" />
                Add Phone Number
              </button>
            </div>

          {/* Call Results Display */}
          {latestSummary && (
              <div className="mt-8 p-4 border border-border rounded-lg bg-card">
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Latest Call Results</h3>
                  <CallResult
                      result={latestSummary}
                  />
              </div>
          )}

          {/* Status Display */}
          <div className="mt-4 text-center text-sm text-gray-600">
              {callStatus !== 'idle' && (
                  <div>
                      Overall Status: <span className={`font-medium px-1.5 py-0.5 rounded ${
                          callStatus === 'completed' ? 'bg-green-100 text-green-800' :
                          callStatus === 'failed' ? 'bg-red-100 text-red-800' :
                          callStatus === 'in-progress' || callStatus === 'initiating-call' ? 'bg-blue-100 text-blue-800' :
                          callStatus === 'polling-results' ? 'bg-yellow-100 text-yellow-800' :
                          callStatus === 'loading-script' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                      }`}>
                          {callStatus.replace('-', ' ')}
                      </span>
                  </div>
              )}
              {lastError && <div className="mt-2 text-red-600">{lastError}</div>}
          </div>

          {/* Audio Status Display for Local Testing */}
          {audioMode === 'local' && (isRecording || isConnected || audioStatus !== 'Ready to start local testing') && (
            <div className="mt-4 text-center space-y-2">
              <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                isConnected ? 'bg-green-100 text-green-800' :
                isRecording ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {audioStatus}
              </span>
              {/* Visual countdown timer */}
              {aiInitTimer > 0 && (
                <div className="mt-2">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full text-xl font-bold ${
                    aiInitTimer <= 3 ? 'bg-red-100 text-red-800' :
                    aiInitTimer <= 5 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {aiInitTimer}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">AI initialization countdown</p>
                </div>
              )}
            </div>
          )}
            </form>
          ) : (
            <div className="space-y-6">

            <form onSubmit={(e) => e.preventDefault()} className="space-y-6">
              {/* Country Selection Dropdown */}
              <div>
                <label htmlFor="incomingCountrySelect" className="block text-sm font-medium mb-2 text-foreground">
                  Select Country / Twilio Number
                </label>
                <select
                  id="incomingCountrySelect"
                  value={selectedCountry}
                  onChange={(e) => setSelectedCountry(e.target.value)}
                  className={cn(
                    "w-full p-2 border border-border rounded-md shadow-sm",
                    "focus:ring-primary focus:border-primary bg-input text-foreground",
                    "transition-colors duration-200"
                  )}
                >
                  {AVAILABLE_COUNTRIES.map((country) => (
                    <option key={country.code} value={country.code}>
                      {country.flag} {country.name} ({country.phoneNumber})
                    </option>
                  ))}
                </select>
              </div>

              {/* Model Selection Dropdown */}
              <div>
                <label htmlFor="incomingModelSelect" className="block text-sm font-medium mb-2 text-foreground">
                  Select AI Model
                </label>
                <select
                  id="incomingModelSelect"
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className={cn(
                    "w-full p-2 border border-border rounded-md shadow-sm",
                    "focus:ring-primary focus:border-primary bg-input text-foreground",
                    "transition-colors duration-200"
                  )}
                >
                  {Object.entries(availableModels).map(([modelId, modelInfo]) => (
                    <option key={modelId} value={modelId}>
                      {typeof modelInfo === 'object' && modelInfo !== null && 'name' in modelInfo ? (modelInfo as { name: string }).name : String(modelInfo)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Voice Selection Dropdown */}
              <div>
                <label htmlFor="incomingVoiceSelect" className="block text-sm font-medium mb-2 text-foreground">
                  Select AI Voice Sound
                </label>
                <select
                  id="incomingVoiceSelect"
                  value={selectedVoice}
                  onChange={(e) => setSelectedVoice(e.target.value)}
                  className={cn(
                    "w-full p-2 border border-border rounded-md shadow-sm",
                    "focus:ring-primary focus:border-primary bg-input text-foreground",
                    "transition-colors duration-200"
                  )}
                >
                  {availableVoices.map((voice) => {
                    const details = voiceDetails[voice] as { 
                      gender?: string; 
                      characteristics?: string;
                      description?: string;
                      useCase?: string;
                    } | undefined;
                    
                    // Use voice descriptions from .env if available, otherwise fall back to voiceDetails
                    const envDescription = voiceDescriptions[voice];
                    const displayText = envDescription || (details
                      ? `${voice} - ${details.gender || 'Unknown'} (${details.characteristics || 'No description'})`
                      : voice.charAt(0).toUpperCase() + voice.slice(1));
                    
                    return (
                      <option 
                        key={voice} 
                        value={voice} 
                        className="bg-input text-foreground"
                      >
                        {displayText}
                      </option>
                    );
                  })}
                </select>
                
                {selectedVoice && voiceDetails[selectedVoice] ? (() => {
                  const details = voiceDetails[selectedVoice];
                  return (
                    <div className="mt-2 p-3 bg-muted rounded-md">
                      <div className="text-sm space-y-2">
                        <div>
                          <span className="font-medium text-foreground">{selectedVoice}</span>
                          {details.gender && (
                            <span className="ml-2 px-2 py-0.5 bg-primary/10 text-primary text-xs rounded">
                              {details.gender}
                            </span>
                          )}
                        </div>
                        {details.characteristics && (
                          <div className="text-muted-foreground">
                            <strong>Voice characteristics:</strong> {details.characteristics}
                          </div>
                        )}
                        {details.pitch && (
                          <div className="text-muted-foreground">
                            <strong>Pitch:</strong> {details.pitch}
                          </div>
                        )}
                        {details.timbre && (
                          <div className="text-muted-foreground">
                            <strong>Timbre:</strong> {details.timbre}
                          </div>
                        )}
                        {details.persona && (
                          <div className="text-muted-foreground">
                            <strong>Persona:</strong> {details.persona}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })() : null}
              </div>

              {/* Language Selection Dropdown */}
              <div>
                <label htmlFor="incomingLanguageSelect" className="block text-sm font-medium mb-2 text-foreground">
                  Select Output Language
                </label>
                <select
                  id="incomingLanguageSelect"
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className={cn(
                    "w-full p-2 border border-border rounded-md shadow-sm",
                    "focus:ring-primary focus:border-primary bg-input text-foreground",
                    "transition-colors duration-200"
                  )}
                >
                  {AVAILABLE_LANGUAGES.map((lang) => (
                    <option key={lang.code} value={lang.code}>
                      {lang.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Campaign Load Buttons - Incoming Format (Same as Outbound) */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-foreground">
                  Load Incoming Campaign Script (Available Campaigns)
                </label>

                {/* Header Row - Category Labels */}
                <div className="grid grid-cols-6 gap-2 text-xs font-semibold text-muted-foreground text-center">
                  <div className="p-2 bg-accent/20 rounded">English Insurance</div>
                  <div className="p-2 bg-accent/20 rounded">English Fundraising</div>
                  <div className="p-2 bg-accent/20 rounded">Spanish Insurance</div>
                  <div className="p-2 bg-accent/20 rounded">Spanish Fundraising</div>
                  <div className="p-2 bg-accent/20 rounded">Czech Insurance</div>
                  <div className="p-2 bg-accent/20 rounded">Czech Fundraising</div>
                </div>

                {/* Campaign Buttons Grid - 6 Buttons (1-6) for Incoming Scripts (loads 7-12) */}
                <div className="grid grid-cols-6 gap-2">
                  <button
                    type="button"
                    onClick={() => loadCampaignScript(7)}
                    disabled={isLoadingScript}
                    className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                    <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                    1 (In)
                  </button>
                  <button
                    type="button"
                    onClick={() => loadCampaignScript(8)}
                    disabled={isLoadingScript}
                    className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                    <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                    2 (In)
                  </button>
                  <button
                    type="button"
                    onClick={() => loadCampaignScript(9)}
                    disabled={isLoadingScript}
                    className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                    <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                    3 (In)
                  </button>
                  <button
                    type="button"
                    onClick={() => loadCampaignScript(10)}
                    disabled={isLoadingScript}
                    className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                    <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                    4 (In)
                  </button>
                  <button
                    type="button"
                    onClick={() => loadCampaignScript(11)}
                    disabled={isLoadingScript}
                    className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                    <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                    5 (In)
                  </button>
                  <button
                    type="button"
                    onClick={() => loadCampaignScript(12)}
                    disabled={isLoadingScript}
                    className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  >
                    <DocumentArrowDownIcon className="h-4 w-4 inline mr-1"/>
                    6 (In)
                  </button>
                </div>
              </div>

              {/* Task Description Input - Incoming Call Script */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label htmlFor="incomingTaskDescription" className="block text-sm font-medium text-foreground">
                    Campaign Script / Task Description (Text Format - JSON supported)
                  </label>
                </div>
                <textarea
                  id="incomingTaskDescription"
                  value={task}
                  onChange={(e) => setTask(e.target.value)}
                  className="w-full p-2 border border-border rounded-md shadow-sm focus:ring-primary focus:border-primary font-mono text-xs bg-input text-foreground"
                  rows={15}
                  placeholder="Load a campaign script using the buttons above or paste text/JSON here..."
                  required
                />
              </div>

              {/* Call Interface - Same UI for both Local and Twilio */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold mb-2 text-foreground">
                  {audioMode === 'local' ? 'Local Audio Testing (Browser)' : 'Incoming Call Testing'}
                </h3>
                {audioMode === 'local' ? (
                  /* Local Testing Interface */
                  <div className="border border-border rounded-lg p-4 bg-card">
                    <div className="text-center space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Test incoming call scripts using your browser microphone and speakers
                      </p>
                      <div className="flex justify-center gap-4">
                        <button
                          type="button"
                          onClick={isRecording ? () => stopLocalTest(0) : () => handleLocalTest({ name: 'Local Test', phone: 'N/A' }, 0)}
                          disabled={isProcessing}
                          className={cn(
                            "px-6 py-3 rounded-md font-medium transition-colors",
                            isRecording
                              ? "bg-red-600 text-white hover:bg-red-700"
                              : "bg-green-600 text-white hover:bg-green-700",
                            "disabled:opacity-50 disabled:cursor-not-allowed"
                          )}
                        >
                          {isRecording ? 'Stop Test' : 'Start Test'}
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  /* Twilio Incoming Call Interface */
                  <div className="border border-border rounded-lg p-4 bg-card">
                    <div className="text-center space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Incoming calls will be handled automatically when enabled.
                        Call your Twilio number: <strong>{fromPhoneNumber}</strong>
                      </p>
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <p className="text-sm text-blue-800">
                          <strong>Status:</strong> {incomingCallsEnabled ? 'Ready to receive calls' : 'Incoming calls disabled'}
                        </p>
                        {incomingCallsEnabled && (
                          <p className="text-xs text-blue-600 mt-1">
                            Using campaign script: {task ? 'Custom script loaded' : 'Default Campaign 1'}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}



                  {/* Error Display */}
                  {lastError && <div className="mt-2 text-red-600">{lastError}</div>}
              </div>

              {/* Audio Status Display for Local Testing */}
              {audioMode === 'local' && (isRecording || isConnected || audioStatus !== 'Ready to start local testing') && (
                <div className="mt-4 text-center space-y-2">
                  <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                    isConnected ? 'bg-green-100 text-green-800' :
                    isRecording ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {audioStatus}
                  </span>
                  {aiInitTimer > 0 && (
                    <div className="text-sm text-muted-foreground">
                      AI initializing in {aiInitTimer}s...
                    </div>
                  )}
                </div>
              )}
            </form>
            </div>
          )}



        <Toaster position="bottom-right" />
      </div>
    </main>
    </div>
  );
}
